import math
from gcode_defect.GcodeParse.ModelInfo import ModelInfo, LayerInfo, TrackInfo, MotionType, LineType, DotType

def g_str2flt(num_str: str) -> float:
    """将G代码中的数字字符串转换为浮点数"""
    # 处理特殊格式:".123"->"0.123"或 "-.123"->"-0.123
    if num_str.startswith('.'):
        num_str ='0'+ num_str
    elif num_str.startswith('-.'):
        num_str ='-0.' + num_str[2:]
    try:
        return float(num_str)
    except ValueError:
        print(f"Invalid number format: '{num_str}'")
        return 0.0

class GCodeProcess:
    def __init__(self, file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:#判断文件是否正确
                pass
        except FileNotFoundError:
            print(file_path + " is not exist")
        except Exception as e:
            print(f"Error processing file: {str(e)}")

        self.file_path = file_path
        self.p_hotend_pos = (0, 0, 0)
        self.hotend_speed = 0
        self.track_width_comment = 0
        self.speed = 0
        self.accel = 0
        self.track_lineType = None
        self.isWiping = False
        self.isChangeLayer=False
        self.isPrinting=False

    def extract_G1_2_3(self, parts: list[str]) -> TrackInfo:
        track = TrackInfo(st_pos=self.p_hotend_pos, speed=self.hotend_speed)

        #具体坐标
        (x, y, z) = track.stPos
        (i, j) = (0, 0)
        for part in parts[1:]:
            if part.startswith('X'):
                x = g_str2flt(part[1:])
            if part.startswith('Y'):
                y = g_str2flt(part[1:])
            if part.startswith('Z'):
                z = g_str2flt(part[1:])
            if part.startswith('I'):
                i = g_str2flt(part[1:])
            if part.startswith('J'):
                j = g_str2flt(part[1:])
            if part.startswith('E'):
                track.ex = g_str2flt(part[1:])
            if part.startswith('F'):
                self.speed = g_str2flt(part[1:])
        track.endPos = (x, y, z)
        if parts[0] == "G2" or parts[0] == "G3":
            track.cicPos = (track.stPos[0]+i, track.stPos[1]+j, track.stPos[2])

        # 速度与加速度
        track.speed=self.speed
        track.accel=self.accel

        # 轨迹类型
        track.motionType = MotionType(parts[0])
        if track.stPos==track.endPos:
            track.motionType = MotionType.pause

        #线长/宽
        track.calc_track_length()
        track.width_comment = self.track_width_comment if track.ex > 0 else 0
        
        #LineType/DotType
        track.lineType = self.track_lineType if not self.isWiping else LineType.Wipe
        if track.motionType == MotionType.pause:
            if track.ex < 0:
                track.dotType = DotType.Withdraw
            elif track.ex > 0:
                track.dotType = DotType.Reload
        if track.ex == 0:
            if track.endPos != track.stPos:
                track.lineType = LineType.Empty_move  
            else: 
                track.dotType = DotType.AdjustPara
        if track.dotType is not None:
            track.lineType = None
        return track
        
    def extract_head_comment(self, line, params_dict):
        if not line:
            return None

        line=line.strip(';').strip()# 移除行首分号和空格
        #处理特殊情况
        if '=' in line:# CASE: change_filament_gcode = ;======== H2D ========\n;===== 20250604 =====\n……
            key, value =line.split('=',1)
            key = key.strip()
            value = value.strip()
            if key.endswith('gcode'):
                params_dict[key] = value
                return None
        elif line.startswith('BambuStudio'):  # CASE: BambuStudio ***********
            params_dict['BambuStudio'] = line.split(' ')[1]
            return None
        elif line.startswith('model printing time'): # CASE: ; model printing time: 8h 0m 17s; total estimated time: 8h 9m 51s
            two_time = line.split(';')
            params_dict['model printing time'] =two_time[0].split(':')[1].strip()
            params_dict['total estimated time'] = two_time[1].split(':')[1].strip()
            return None

        # 分割键和值
        if ':' in line:
            key, value = line.split(':', 1)
        elif '=' in line:
            key, value = line.split('=', 1)
        else:
            return None
        key = key.strip()
        value = value.strip()

        if not value:
            params_dict[key] = ""
        # 处理多值情况
        elif ',' in value or ';' in value:
            values = []
            str_values=value.split(',') if ',' in value else value.split(';')
            for v in str_values:
                v = v.strip()
                try:
                    # 尝试转换为数字
                    num = float(v)
                    values.append(num)
                except ValueError:
                    values.append(v)
            params_dict[key] = values
        else:
            # 单值处理
            try:
                params_dict[key] = float(value)
            except ValueError:
                params_dict[key] = value
        return params_dict
    
    def extract_intext_comment(self, line: str):
        # 线型注释
        if line.startswith('; FEATURE:'):
            feature=line.split(":")[1].strip()
            try:
                self.track_lineType=LineType(feature)
            except ValueError:
                self.track_lineType=None
        elif line.startswith('; WIPE_START'):
            self.isWiping=True
        elif line.startswith('; WIPE_END'):
            self.isWiping=False

        # 线宽注释
        elif line.startswith('; LINE_WIDTH:'):
            self.track_width_comment=g_str2flt(line.split(":")[1].strip())

    def extract_gcode_line(self, line: str):
        """
        处理单行G代码
        """
        # 提取注释行
        if line.startswith(';'):
            self.extract_intext_comment(line)
            return -1
        
        # 提取数据行
        data=line.split(';')[0]
        parts = data.split(' ')

        # G1/G2/G3
        if parts[0] == "G1" or parts[0] == "G2" or parts[0] == "G3":
            track = self.extract_G1_2_3(parts)
            return track

        # M991 S0 P{layer_num}; notify layer change
        if parts[0] == "M991" and int(parts[2][1:]) >= 0: 
            self.isPrinting=True
            self.isChangeLayer=True

        # M204 S1000; set acceleration
        if parts[0] == "M204":
            for part in parts[1:]:
                if part.startswith('S'):
                    self.accel=g_str2flt(part[1:])

        return -1
    
    def get_printing_paras(self):
        """Extract printing parameters from G-code file"""
        line_num=0
        paras_dict = {}
        isReadingHead = False
        isReadingConfig = False
        isReadEnd = False

        with open(self.file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line_num += 1
                if not line or line == '\n':# 跳过空行
                    continue
                
                if line.startswith('; HEADER_BLOCK_START'):
                    isReadingHead = True
                elif line.startswith('; HEADER_BLOCK_END'):
                    isReadingHead = False
                # 打印参数
                elif line.startswith('; CONFIG_BLOCK_START'):
                    isReadingConfig = True
                elif line.startswith('; CONFIG_BLOCK_END'):
                    isReadingConfig = False
                    isReadEnd = True

                if isReadingHead or isReadingConfig:# 提取头部的参数信息
                    self.extract_head_comment(line, paras_dict)

                if isReadEnd:
                    return paras_dict, line_num
        return paras_dict, 0 
    
    def g_file2model(self, chord_error_mm = 0.005, max_discrete_num = 100) -> ModelInfo:
        layer=LayerInfo()
        model = ModelInfo()
        
        # Extract printing parameters
        model.paras, para_line_num = self.get_printing_paras()

        with open(self.file_path, 'r', encoding='utf-8') as f:
            line_num = 0
            for line in f:
                line_num += 1
                # Skip comment para head
                if line_num < para_line_num:
                    continue

                # 跳过空行
                if not line or line == '\n':
                    continue
                
                # 提取单行gcode的信息
                ret= self.extract_gcode_line(line)
                if not self.isPrinting:
                    continue

                # if not G1/G2/G3
                if ret == -1:
                    if self.isChangeLayer:
                        self.isChangeLayer = False
                        layer = LayerInfo()
                        model.layers.append(layer)
                    continue

                # if G1/G2/G3      
                ret.codeLineNum = line_num
                ret.layer_id = len(model.layers) - 1
                ret.chord_error_mm = chord_error_mm
                ret.max_discrete_num = max_discrete_num
                layer.tracks.append(ret)

                # 更新设备热端当前参数，作为下个track的起始参数
                self.p_hotend_pos = ret.endPos
                self.hotend_speed = ret.speed

            # must h->w->pts
            model.update_layer_height()
            model.update_track_width()
            model.store_discrete_data()
            return model
        return None