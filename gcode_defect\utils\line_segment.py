from __future__ import annotations
from dataclasses import dataclass, field
import numpy as np
import math
from gcode_defect.GcodeParse.ModelInfo import TrackInfo, MotionType

@dataclass
class LineSegment:
    """
    A class to represent a line segment with its geometric properties and width.

    Attributes:
        start (np.ndarray): The (x, y) starting point of the line.
        end (np.ndarray): The (x, y) ending point of the line.
        initial_width (float): The default width of the line.
        origin_track (TrackInfo): The original TrackInfo object this segment came from.
        updated_width (float): The width after processing overlaps. Initialized to initial_width.
        vector (np.ndarray): The vector from start to end.
        length (float): The length of the line segment.
        normalized_vector (np.ndarray): The unit vector representing the line's direction.
    """
    start: np.ndarray
    end: np.ndarray
    initial_width: float
    origin_track: TrackInfo
    updated_width: float = field(init=False)

    # Post-initialization to calculate derived properties
    def __post_init__(self):
        self.updated_width = self.initial_width
        self.vector = self.end - self.start
        self.length = np.linalg.norm(self.vector)

        if self.length > 1e-9:
            self.normalized_vector = self.vector / self.length
        else:
            self.normalized_vector = np.array([0.0, 0.0])

    def get_axis_aligned_bounding_box(self) -> tuple:
        """Calculates the AABB for the line, accounting for its width."""
        from gcode_defect.utils.poly import get_line_width_polygon_points
        pts = get_line_width_polygon_points(self.start, self.end, self.updated_width)
        corners = np.array(pts)

        min_coords = np.min(corners, axis=0)
        max_coords = np.max(corners, axis=0)

        return (min_coords[0], min_coords[1], max_coords[0], max_coords[1])

def get_arc_angles_from_center(start: tuple, end: tuple, center: tuple, clockwise: bool):
    """Calculates the start and end angles of an arc relative to its center."""
    start, end, center = np.array(start), np.array(end), np.array(center)
    # Calculate angles in radians
    vec_start = start - center
    vec_end = end - center
    if np.linalg.norm(vec_start) == 0 or np.linalg.norm(vec_end) == 0:
        raise ValueError("Invalid arc: start or end point is the same as center")
    
    start_angle =np.arctan2(vec_start[1], vec_start[0])
    end_angle = np.arctan2(vec_end[1], vec_end[0])
    
    # Revise angle by direction
    if clockwise and end_angle >= start_angle:
        end_angle -= 2*math.pi
    elif not clockwise and end_angle <= start_angle:
        end_angle += 2*math.pi
    return start_angle, end_angle

def get_arc_sample_points(start: tuple, end: tuple, center: tuple, clockwise: bool,
                        chord_error_mm: float, max_discrete_num: int):
    """Sample points along an arc defined by its center, radius, and start/end angles."""
    start, end, center = np.array(start), np.array(end), np.array(center)

    def _calc_arc_discrete_num():
        """Calculate the number of discrete points to sample an arc track, based on the chord error."""   
        t = chord_error_mm / radius
        if radius <= 1e-9 or chord_error_mm <= 0 or t >= 1:
            return 2 # Only endpoints
        
        alpha = np.arccos(1 - t)
        if alpha < 1e-9:
            return max_discrete_num
 
        delta_angle = abs(end_angle - start_angle)
        num_segments = math.ceil(delta_angle / (2 * alpha))
        n_pts = int(num_segments) + 1
        return min(max_discrete_num, n_pts)
    
    start_angle, end_angle = get_arc_angles_from_center(start, end, center, clockwise)
    # The radius for arc calculation should be in the XY plane.
    radius = np.linalg.norm(center[:2] - start[:2])
    n_pts = _calc_arc_discrete_num()
    if n_pts <= 2:
        sample_angles = [start_angle, end_angle]
        sample_pts = [start, end]
        # sample_pts = (tuple(start), tuple(end))
    else:
        sample_angles = np.linspace(start_angle, end_angle, n_pts)
        sample_pts = [center[:2] + radius * np.array([np.cos(a), np.sin(a)]) for a in sample_angles]
        z_col = np.linspace(start[2], end[2], n_pts)
        sample_pts = [np.append(pt, z) for pt, z in zip(sample_pts, z_col)]
        # sample_pts = tuple([tuple(pt) for pt in sample_pts])
    return sample_angles, sample_pts


def translate_continious_tracks_into_pts(contour: list[TrackInfo], is_G1_discrete = False, G1_samp_length = 1) -> list[tuple[float, float]]:
    """Translate a continious contour into a list of points"""
    points = []
    last_end_pos = None
    for track in contour:
        if last_end_pos and track.stPos != last_end_pos:
            raise ValueError(f"Track is not continuous.")
        last_end_pos = track.endPos

        if track.motionType == MotionType.G1:
            if is_G1_discrete:
                points.extend(track.get_line_sample_pts(G1_samp_length)[:-1])  # Exclude last point to avoid duplication
            else:
                points.append(track.stPos)
        elif track.motionType in [MotionType.G2, MotionType.G3]:
            arc_points = track.get_arc_sample_paras()['sample_points']
            points.extend(arc_points[:-1])

    points.append(contour[-1].endPos)
    return points

def discretize_tracks(tracks: list[TrackInfo], samp_length: float) -> list[LineSegment]:
    """
    Breaks down a list of TrackInfo objects into smaller, straight-line LineSegment objects.
    This is useful for detailed analysis of curved paths.

    Args:
        tracks: A list of TrackInfo objects to discretize.
        samp_length: The target length for each small segment, used for G1 lines.

    Returns:
        A flat list of LineSegment objects representing the discretized paths.
    """
    segments = []
    for track in tracks:
        if track.motionType == MotionType.G1:
            samp_pts = track.get_line_sample_pts(samp_length)
        elif track.motionType in [MotionType.G2, MotionType.G3]:
            # For arcs, use the pre-sampled points which are already finely discretized.
            samp_pts = track.get_arc_sample_paras()['sample_points']
        else:
            # Skip non-movement tracks like pauses
            continue
        
        if samp_pts is None or len(samp_pts) < 2:
            continue

        for i in range(len(samp_pts) - 1):
            start_pt = np.array(samp_pts[i])
            end_pt = np.array(samp_pts[i+1])
            segment = LineSegment(
                start=start_pt,
                end=end_pt,
                initial_width=track.width,
                origin_track=track
            )
            segments.append(segment)
    return segments