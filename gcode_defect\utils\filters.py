import numpy as np
from scipy.spatial import KDTree
from sklearn.cluster import DBSCAN

from gcode_defect.GcodeParse.ModelInfo import MotionType, TrackInfo

def find_sharp_corners_track_contour(contour: list[TrackInfo], angle_threshold_deg: float, look_ahead_dist: float = 1.0) -> list[tuple[float, float]]:
    from gcode_defect.utils.line_segment import translate_continious_tracks_into_pts

    points_list = translate_continious_tracks_into_pts(contour, is_G1_discrete=False)
    return find_sharp_corners_pts(points_list, angle_threshold_deg, look_ahead_dist)

def find_sharp_corners_pts(pts: list[np.ndarray], angle_threshold_deg: float, look_ahead_dist: float = 1.0) -> list[tuple[float, float]]:
    """
    Finds vertices in a list of contours where the angle is sharper than a given threshold.
    Uses a "look-ahead" distance to measure the angle over a larger scale, making it robust against small fillets.

    Args:
        contour: contour is a list of connected TrackInfo objects.
        angle_threshold_deg: The angle in degrees. Corners sharper than this will be returned.
        look_ahead_dist: The distance (in mm) to look forward and backward from a vertex to calculate the angle.

    Returns:
        A list of (x, y) coordinates for each sharp corner.
    """
    if not pts:
        return []
    
    sharp_corners = []
    angle_threshold_rad = np.deg2rad(angle_threshold_deg)

    # Remove duplicate points
    points_list = [p[:2] for p in pts]
    points_set = set(map(tuple, points_list))
    points = np.array(list(points_set))

    if len(points) < 3:
        return []

    # Check if the contour is closed and remove the last point.
    is_close = np.linalg.norm(points[0] - points[-1]) <= 0.2
    if is_close:
        points = points[:-1]
        if len(points) < 3:
            return []

    # Calculate segments and cumulative distances
    if is_close:
        all_segments = np.diff(points, axis=0, append=points[0:1])
    else:
        all_segments = np.diff(points, axis=0)

    segment_lengths = np.linalg.norm(all_segments, axis=1)
    cumulative_dists = np.insert(np.cumsum(segment_lengths), 0, 0)
    total_length = cumulative_dists[-1]

    if total_length < look_ahead_dist * 2.01:
        look_ahead_dist = look_ahead_dist / 2 #if contour is short, use shorter look_ahead_dist

    # Determine which vertices to check.
    loop_range = range(len(points)) if is_close else range(1, len(points) - 1)

    for i in loop_range:
        p2 = points[i]
        current_dist = cumulative_dists[i]

        # Find target distances for look-ahead and look-behind
        dist_before = current_dist - look_ahead_dist
        dist_after = current_dist + look_ahead_dist

        # Use a temporary, extended array for interpolation on closed loops to handle wrap-around.
        if is_close:
            points_closed_x = np.append(points[:, 0], points[0, 0])
            points_closed_y = np.append(points[:, 1], points[0, 1])

            # Create extended arrays for seamless interpolation across the wrap-around point.
            interp_dists = np.hstack([cumulative_dists - total_length, cumulative_dists, cumulative_dists + total_length])
            interp_points_x = np.tile(points_closed_x, 3)
            interp_points_y = np.tile(points_closed_y, 3)

            p1_x = np.interp(dist_before, interp_dists, interp_points_x)
            p1_y = np.interp(dist_before, interp_dists, interp_points_y)
            p3_x = np.interp(dist_after, interp_dists, interp_points_x)
            p3_y = np.interp(dist_after, interp_dists, interp_points_y)
        else:
            p1_x = np.interp(dist_before, cumulative_dists, points[:, 0])
            p1_y = np.interp(dist_before, cumulative_dists, points[:, 1])
            p3_x = np.interp(dist_after, cumulative_dists, points[:, 0])
            p3_y = np.interp(dist_after, cumulative_dists, points[:, 1])

        p1 = np.array([p1_x, p1_y])
        p3 = np.array([p3_x, p3_y])

        # Create vectors from the vertex P2 to the look-ahead points
        v1 = p1 - p2
        v2 = p3 - p2

        norm_v1 = np.linalg.norm(v1)
        norm_v2 = np.linalg.norm(v2)
        if norm_v1 < 1e-6 or norm_v2 < 1e-6:
            continue

        dot_product = np.dot(v1, v2)
        cos_angle = dot_product / (norm_v1 * norm_v2)
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        angle_rad = np.arccos(cos_angle)

        if angle_rad < angle_threshold_rad:
            sharp_corners.append(tuple(p2))

    return sharp_corners


def filter_by_proximity(points_to_filter: list[tuple[float, float, float]],
                        reference_points: list[tuple[float, float]],
                        distance_threshold: float) -> list[tuple[float, float, float]]:
    """
    Keeps only the 3D points that are farther than a given 2D distance from a set of 2D reference points.

    Args:
        points_to_filter: The list of candidate 3D points (e.g., potential defects).
        reference_points: The list of 2D points to check proximity against (e.g., sharp corners).
        distance_threshold: The minimum allowed 2D distance. Points closer than this will be filtered out.

    Returns:
        A new list of 3D points that are not close to any of the reference points.
    """
    if not reference_points or not points_to_filter:
        return points_to_filter

    # Extract 2D coordinates from the 3D points for comparison
    points_to_filter_2d = [p[:2] for p in points_to_filter]

    # Use KDTree for efficient nearest neighbor search on 2D reference points
    ref_tree = KDTree(reference_points)

    # For each point to filter, find the distance to the nearest reference point in 2D
    distances, _ = ref_tree.query(points_to_filter_2d, k=1)

    # Keep only the points where the nearest distance is greater than the threshold
    filtered_points = [
        point for point, dist in zip(points_to_filter, distances) if dist > distance_threshold
    ]

    return filtered_points


def filter_by_density(points: list[tuple[float, float]],
                      min_cluster_size: int,
                      max_distance: float) -> list[tuple[float, float]]:
    """
    Filters a list of points, keeping only those that are part of a dense cluster.
    Uses the DBSCAN algorithm to identify and remove isolated "noise" points.

    Args:
        points: The list of points to filter.
        min_cluster_size: The minimum number of points required to form a dense region (a cluster).
        max_distance: The maximum distance between two samples for one to be considered as in the neighborhood of the other.

    Returns:
        A new list containing only the points that belong to a cluster.
    """
    if not points or len(points) < min_cluster_size:
        return []

    points_array = np.array(points)

    # DBSCAN will find core samples of high density and expand clusters from them.
    # Points that are not part of any cluster are labeled as -1 (noise).
    db = DBSCAN(eps=max_distance, min_samples=min_cluster_size).fit(points_array)
    labels = db.labels_

    # Keep only the points that were assigned to a cluster (label != -1)
    clustered_points = [
        point for point, label in zip(points, labels) if label != -1
    ]

    return clustered_points