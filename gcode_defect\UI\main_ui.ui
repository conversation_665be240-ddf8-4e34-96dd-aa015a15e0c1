<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1300</width>
    <height>750</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QTabWidget" name="tabWidget">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>1300</width>
      <height>750</height>
     </rect>
    </property>
    <property name="currentIndex">
     <number>1</number>
    </property>
    <widget class="QWidget" name="tab">
     <attribute name="title">
      <string>G-code文件</string>
     </attribute>
     <widget class="QGroupBox" name="groupBox_4">
      <property name="geometry">
       <rect>
        <x>710</x>
        <y>10</y>
        <width>191</width>
        <height>651</height>
       </rect>
      </property>
      <property name="title">
       <string>模型信息</string>
      </property>
      <widget class="QLabel" name="label_model_info_display">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>20</y>
         <width>171</width>
         <height>621</height>
        </rect>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignTop</set>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="groupBox">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>110</y>
        <width>481</width>
        <height>551</height>
       </rect>
      </property>
      <property name="title">
       <string>缺陷检测</string>
      </property>
      <widget class="QTableWidget" name="tableWidget_defectParams">
       <property name="geometry">
        <rect>
         <x>210</x>
         <y>20</y>
         <width>241</width>
         <height>491</height>
        </rect>
       </property>
       <row/>
       <column>
        <property name="text">
         <string>参数名称</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>数值</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>变量名</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>变量类型</string>
        </property>
       </column>
      </widget>
      <widget class="QWidget" name="verticalLayoutWidget">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>20</y>
         <width>131</width>
         <height>441</height>
        </rect>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <widget class="QRadioButton" name="radioButton_defect_overlap">
          <property name="text">
           <string>内墙堆料</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="radioButton_defect_overhang">
          <property name="text">
           <string>悬垂检测</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="radioButton_defect_layerTimeDiff">
          <property name="text">
           <string>层时间差异</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="radioButton_shiftLine">
          <property name="text">
           <string>走线挤压偏移</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="radioButton_surface">
          <property name="text">
           <string>墙壁疤痕（挤压）</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="radioButton_overhang_zseam">
          <property name="text">
           <string>悬垂接缝</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="radioButton_longEmptyDist">
          <property name="text">
           <string>长距离空驶     </string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="radioButton_unified_surface">
          <property name="text">
           <string>统一表面分析</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_analyseDefect">
          <property name="text">
           <string>检测</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QGroupBox" name="groupBox_3">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>10</y>
        <width>681</width>
        <height>91</height>
       </rect>
      </property>
      <property name="title">
       <string>G-code文件导入设置</string>
      </property>
      <widget class="QWidget" name="gridLayoutWidget">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>661</width>
         <height>80</height>
        </rect>
       </property>
       <layout class="QGridLayout" name="gridLayout">
        <item row="1" column="4">
         <widget class="QPushButton" name="pushButton_importFile">
          <property name="text">
           <string>导入</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_19">
          <property name="text">
           <string>G2/3离散弓高误差(mm)：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_2">
          <property name="text">
           <string>路径：</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QLineEdit" name="lineEdit_chord_error">
          <property name="text">
           <string>0.01</string>
          </property>
         </widget>
        </item>
        <item row="1" column="2" colspan="2">
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Policy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>350</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="4">
         <widget class="QPushButton" name="pushButton_chooseFile">
          <property name="text">
           <string>选择文件</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1" colspan="3">
         <widget class="QLineEdit" name="lineEdit_filePath"/>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QGroupBox" name="groupBox_11">
      <property name="geometry">
       <rect>
        <x>510</x>
        <y>110</y>
        <width>191</width>
        <height>551</height>
       </rect>
      </property>
      <property name="title">
       <string>缺陷检测结果</string>
      </property>
      <widget class="QTextEdit" name="textEdit_defect_display">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>20</y>
         <width>171</width>
         <height>511</height>
        </rect>
       </property>
      </widget>
     </widget>
    </widget>
    <widget class="QWidget" name="tab_2">
     <attribute name="title">
      <string>绘图</string>
     </attribute>
     <widget class="QWidget" name="matplotlib_area" native="true">
      <property name="geometry">
       <rect>
        <x>420</x>
        <y>10</y>
        <width>850</width>
        <height>650</height>
       </rect>
      </property>
     </widget>
     <widget class="QGroupBox" name="groupBox_2">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>0</y>
        <width>401</width>
        <height>71</height>
       </rect>
      </property>
      <property name="title">
       <string>2D绘图</string>
      </property>
      <widget class="QWidget" name="horizontalLayoutWidget">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>20</y>
         <width>381</width>
         <height>41</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QLabel" name="label">
          <property name="text">
           <string>层数：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="combo_plot_layerNum"/>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_plot_2D">
          <property name="text">
           <string>2D绘制</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_plot_clf">
          <property name="text">
           <string>清空</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QScrollBar" name="horizontalScrollBar_plot_trackNum">
      <property name="geometry">
       <rect>
        <x>420</x>
        <y>660</y>
        <width>851</width>
        <height>20</height>
       </rect>
      </property>
      <property name="maximum">
       <number>1000</number>
      </property>
      <property name="value">
       <number>1000</number>
      </property>
      <property name="sliderPosition">
       <number>1000</number>
      </property>
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
     </widget>
     <widget class="QGroupBox" name="groupBox_5">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>80</y>
        <width>401</width>
        <height>231</height>
       </rect>
      </property>
      <property name="title">
       <string>线型</string>
      </property>
      <widget class="QWidget" name="horizontalLayoutWidget_3">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>381</width>
         <height>221</height>
        </rect>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="1">
         <widget class="QCheckBox" name="checkBox_isPlot_Outer_wall">
          <property name="text">
           <string>外墙</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QCheckBox" name="checkBox_isPlot_Inner_wall">
          <property name="text">
           <string>内墙</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QCheckBox" name="checkBox_isPlot_Top_surface">
          <property name="text">
           <string>顶面</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QCheckBox" name="checkBox_isPlot_Bottom_surface">
          <property name="text">
           <string>底面</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <widget class="QCheckBox" name="checkBox_isPlot_Overhang_wall">
          <property name="text">
           <string>悬空墙</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="6" column="0">
         <widget class="QCheckBox" name="checkBox_isPlot_Floating_vertical_shell">
          <property name="text">
           <string>浮空垂直壳</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="2" column="2">
         <widget class="QCheckBox" name="checkBox_isPlot_Brim">
          <property name="text">
           <string>Brim</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="3" column="2">
         <widget class="QCheckBox" name="checkBox_isPlot_Gap_infill">
          <property name="text">
           <string>填缝</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QCheckBox" name="checkBox_isPlot_Sparse_infill">
          <property name="text">
           <string>稀疏填充</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QCheckBox" name="checkBox_isPlot_Internal_solid_infill">
          <property name="text">
           <string>实心填充</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QCheckBox" name="checkBox_isPlot_Support">
          <property name="text">
           <string>支撑</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="4" column="1">
         <widget class="QCheckBox" name="checkBox_isPlot_Support_interface">
          <property name="text">
           <string>支撑面</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="5" column="0">
         <widget class="QCheckBox" name="checkBox_isPlot_Bridge">
          <property name="text">
           <string>桥接</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="4" column="2">
         <widget class="QCheckBox" name="checkBox_isPlot_Support_transition">
          <property name="text">
           <string>支撑转换面</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="5" column="1">
         <widget class="QCheckBox" name="checkBox_isPlot_Empty_move">
          <property name="text">
           <string>空驶</string>
          </property>
         </widget>
        </item>
        <item row="5" column="2">
         <widget class="QCheckBox" name="checkBox_isPlot_Wipe">
          <property name="text">
           <string>擦拭</string>
          </property>
         </widget>
        </item>
        <item row="6" column="1">
         <widget class="QCheckBox" name="checkBox_isPlot_Custom">
          <property name="text">
           <string>自定义</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QGroupBox" name="groupBox_6">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>440</y>
        <width>401</width>
        <height>91</height>
       </rect>
      </property>
      <property name="title">
       <string>显示设置</string>
      </property>
      <widget class="QLabel" name="label_4">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>50</y>
         <width>51</width>
         <height>31</height>
        </rect>
       </property>
       <property name="text">
        <string>透明度：</string>
       </property>
      </widget>
      <widget class="QScrollBar" name="horizontalScrollBar_transparant">
       <property name="geometry">
        <rect>
         <x>80</x>
         <y>56</y>
         <width>311</width>
         <height>20</height>
        </rect>
       </property>
       <property name="maximum">
        <number>100</number>
       </property>
       <property name="value">
        <number>100</number>
       </property>
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
      </widget>
      <widget class="QWidget" name="horizontalLayoutWidget_5">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>20</y>
         <width>391</width>
         <height>31</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <widget class="QCheckBox" name="checkBox_isPlot_line_width">
          <property name="text">
           <string>显示线宽</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBox_isPlot_layer_overlap">
          <property name="text">
           <string>叠层绘制</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBox_isPlot_defect">
          <property name="text">
           <string>显示缺陷</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QGroupBox" name="groupBox_7">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>370</y>
        <width>401</width>
        <height>61</height>
       </rect>
      </property>
      <property name="title">
       <string>颜色设置</string>
      </property>
      <widget class="QWidget" name="gridLayoutWidget_3">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>381</width>
         <height>51</height>
        </rect>
       </property>
       <layout class="QGridLayout" name="gridLayout_4">
        <item row="0" column="2">
         <widget class="QRadioButton" name="radioButton_plot_color_width">
          <property name="text">
           <string>线宽</string>
          </property>
         </widget>
        </item>
        <item row="0" column="4">
         <widget class="QRadioButton" name="radioButton_plot_color_gray">
          <property name="text">
           <string>灰模</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QRadioButton" name="radioButton_plot_color_type">
          <property name="text">
           <string>线型</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <widget class="QRadioButton" name="radioButton_plot_color_time">
          <property name="text">
           <string>层时间</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QRadioButton" name="radioButton_plot_color_layer">
          <property name="text">
           <string>整层</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QGroupBox" name="groupBox_8">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>310</y>
        <width>401</width>
        <height>61</height>
       </rect>
      </property>
      <property name="title">
       <string>点</string>
      </property>
      <widget class="QWidget" name="gridLayoutWidget_2">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>381</width>
         <height>51</height>
        </rect>
       </property>
       <layout class="QGridLayout" name="gridLayout_5">
        <item row="0" column="1">
         <widget class="QCheckBox" name="checkBox_isPlot_Seam">
          <property name="text">
           <string>接缝</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QCheckBox" name="checkBox_isPlot_Reload">
          <property name="text">
           <string>装填</string>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <widget class="QCheckBox" name="checkBox_isPlot_AdjustPara">
          <property name="text">
           <string>调整</string>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <widget class="QCheckBox" name="checkBox_isPlot_Withdraw">
          <property name="text">
           <string>回抽</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QGroupBox" name="groupBox_9">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>540</y>
        <width>401</width>
        <height>61</height>
       </rect>
      </property>
      <property name="title">
       <string>2D坐标范围</string>
      </property>
      <widget class="QWidget" name="horizontalLayoutWidget_7">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>20</y>
         <width>381</width>
         <height>31</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_5">
        <item>
         <widget class="QLabel" name="label_8">
          <property name="text">
           <string>x:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEdit_xlim_l">
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_10">
          <property name="text">
           <string>-</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEdit_xlim_h">
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_9">
          <property name="text">
           <string>y:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEdit_ylim_l">
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_11">
          <property name="text">
           <string>-</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEdit_ylim_h">
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QSlider" name="verticalSlider_plot_layerNum">
      <property name="geometry">
       <rect>
        <x>1280</x>
        <y>10</y>
        <width>16</width>
        <height>650</height>
       </rect>
      </property>
      <property name="maximum">
       <number>1000</number>
      </property>
      <property name="value">
       <number>0</number>
      </property>
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <property name="tickInterval">
       <number>0</number>
      </property>
     </widget>
     <widget class="QGroupBox" name="groupBox_10">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>610</y>
        <width>401</width>
        <height>61</height>
       </rect>
      </property>
      <property name="title">
       <string>3D绘图</string>
      </property>
      <widget class="QWidget" name="horizontalLayoutWidget_4">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>20</y>
         <width>381</width>
         <height>41</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <widget class="QPushButton" name="pushButton_plot_3D">
          <property name="text">
           <string>3D绘制</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="radioButton_plt_one_layer_3D">
          <property name="text">
           <string>单层</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="radioButton_plt_model_3D">
          <property name="text">
           <string>模型</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBox_isRender_whole_model">
          <property name="text">
           <string>全部渲染</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </widget>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1300</width>
     <height>33</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
