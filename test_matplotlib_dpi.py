#!/usr/bin/env python3
"""
Test script to verify matplotlib DPI settings are working correctly.
Run this to check if the matplotlib configuration improvements are applied.
"""

import sys
import matplotlib
from matplotlib.figure import Figure
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

# Import our plotter to test the configuration
from gcode_defect.Display.plotter2D import plotter2D

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Matplotlib DPI Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Test our plotter
        self.plotter = plotter2D()
        self.plotter.init_widget(central_widget)
        
        # Add some test content
        if self.plotter.ax:
            self.plotter.ax.plot([0, 1, 2, 3], [0, 1, 4, 9], 'ro-', linewidth=2)
            self.plotter.ax.set_title(f"Test Plot - DPI: {self.plotter.fig.dpi}")
            self.plotter.ax.grid(True, alpha=0.3)
            self.plotter.canvas.draw()

def main():
    # Enable high DPI support
    QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    
    app = QApplication(sys.argv)
    
    print("=== Matplotlib Configuration Test ===")
    print(f"Matplotlib version: {matplotlib.__version__}")
    print(f"Backend: {matplotlib.get_backend()}")
    print(f"Default DPI: {matplotlib.rcParams['figure.dpi']}")
    print(f"Save DPI: {matplotlib.rcParams['savefig.dpi']}")
    print(f"Font size: {matplotlib.rcParams['font.size']}")
    print("=====================================")
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
