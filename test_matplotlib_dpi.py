#!/usr/bin/env python3
"""
Test script to verify high-DPI matplotlib rendering improvements.
This tests our custom HighDPIFigureCanvas implementation.
"""

import sys
import matplotlib
from matplotlib.figure import Figure
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, Q<PERSON>abel
from PySide6.QtCore import Qt

# Import our plotter to test the high-DPI configuration
from gcode_defect.Display.plotter2D import plotter2D, HighDPIFigureCanvas

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("High-DPI Matplotlib Test")
        self.setGeometry(100, 100, 900, 700)

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Add info label
        info_label = QLabel()
        device_pixel_ratio = self.devicePixelRatio()
        info_text = f"Device Pixel Ratio: {device_pixel_ratio}\n"
        info_text += f"Matplotlib Backend: {matplotlib.get_backend()}\n"
        info_text += f"Using Custom HighDPIFigureCanvas: Yes"
        info_label.setText(info_text)
        layout.addWidget(info_label)

        # Test our plotter with high-DPI support
        self.plotter = plotter2D()
        self.plotter.init_widget(central_widget)

        # Add test content with fine details to show quality difference
        if self.plotter.ax:
            # Plot with fine details that would show quality issues
            import numpy as np
            x = np.linspace(0, 10, 1000)
            y1 = np.sin(x * 2) * np.exp(-x/5)
            y2 = np.cos(x * 3) * np.exp(-x/7)

            self.plotter.ax.plot(x, y1, 'b-', linewidth=1, label='sin(2x)·e^(-x/5)')
            self.plotter.ax.plot(x, y2, 'r--', linewidth=1, label='cos(3x)·e^(-x/7)')

            # Add text and annotations that would show font quality
            self.plotter.ax.set_title(f"High-DPI Test - Canvas DPI adapts to device ratio", fontsize=12)
            self.plotter.ax.set_xlabel("X axis with fine grid lines", fontsize=10)
            self.plotter.ax.set_ylabel("Y axis", fontsize=10)
            self.plotter.ax.legend(fontsize=9)
            self.plotter.ax.grid(True, alpha=0.3, linewidth=0.5)

            # Add some annotations with small text
            self.plotter.ax.annotate('Peak', xy=(1.5, 0.8), xytext=(3, 0.6),
                                   arrowprops=dict(arrowstyle='->', color='black', lw=0.5),
                                   fontsize=8)

            self.plotter.canvas.draw()

            print(f"Figure DPI: {self.plotter.fig.dpi}")
            print(f"Canvas Device Pixel Ratio: {self.plotter.canvas.devicePixelRatio()}")

def main():
    app = QApplication(sys.argv)

    print("=== High-DPI Matplotlib Test ===")
    print(f"Matplotlib version: {matplotlib.__version__}")
    print(f"Backend: {matplotlib.get_backend()}")
    print("Testing custom HighDPIFigureCanvas...")
    print("=====================================")

    window = TestWindow()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
