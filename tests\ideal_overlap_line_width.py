import math
import matplotlib.pyplot as plt
import numpy as np

def _calculate_overflow_volumn(overlap_width_list, overlap_length_list, layer_height, k_factor):
    r = layer_height / 2
    overflow_area = []
    for ovp_width in overlap_width_list:
        if ovp_width < layer_height:
            # calculate the overlap area of two circle with a diameter of layer_height and a overlap depth of avg_ovp_width
            d = r - ovp_width / 2
            theta = math.acos(d/r)
            overlap_area = r**2 * (theta * 2 - math.sin(2 * theta))
            empty_area = (1 - math.pi / 4) * layer_height**2 - layer_height * ovp_width + overlap_area
            oa = overlap_area - empty_area * (0.5 + k_factor * 0.5)
            if oa > 0:
                overflow_area.append(oa)
            else:
                overflow_area.append(0)
        else:
            overlap_area = math.pi * r**2 + (ovp_width - layer_height) * layer_height
            overflow_area.append(overlap_area)

    overflow_volume = sum (np.array(overflow_area) * np.array(overlap_length_list))

    return overflow_volume

def get_equavilent_width(width, height):
    ideal_area = (width - height) * height + math.pi * height**2 / 4
    return ideal_area / height

if __name__ == '__main__':
    ideal_layer_height = 0.2
    ideal_width = [0.84, 0.82, 0.8, 0.76, 0.72, 0.68, 0.64, 0.6, 0.56]
    # k_factor = 0
    length = 1.0
    
    overlap_width = 0.84 - np.array(ideal_width)
    real_height = [0.2, 0.22, 0.21, 0.23, 0.22, 0.23, 0.22, 0.23, 0.22]
    real_width = [0.85, 0.81, 0.77, 0.76, 0.75, 0.74, 0.73, 0.73, 0.77]
    
    refer_real_width = ideal_layer_height * np.array(ideal_width) / np.array(real_height)
    
    plt.subplot(1, 2, 1)
    for k_factor in [0, 0.2, 0.4 , 0.6, 0.8, 1.0]:
        expand_width = []
        for ow, rh in zip(overlap_width, real_height):
            calc_overflow_volume = _calculate_overflow_volumn([ow], [length], layer_height=ideal_layer_height, k_factor=k_factor)
            # calc_overflow_volume = _calculate_overflow_volumn([ow], [length], layer_height=0.25, k_factor=k_factor)
            expand_width.append(calc_overflow_volume/ideal_layer_height/length)
        calc_width = np.array(ideal_width) + np.array(expand_width)
        calc_width = [get_equavilent_width(w, ideal_layer_height) if w <0.8 else w for w in calc_width]
        plt.plot(overlap_width, calc_width, label=f'calc_width, k ={k_factor}')
    
    plt.plot(overlap_width, real_width, label='real_width')
    plt.plot(overlap_width, ideal_width, label='ideal_width')
    plt.plot(overlap_width, refer_real_width, label='refer_real_width')
    plt.ylim(0, 1)
    plt.legend()
    plt.subplot(1, 2, 2)
    #relative error
    plt.plot(ideal_width, abs(np.array(real_width) - np.array(calc_width))/np.array(real_width), label='error')
    plt.ylim(0, 0.2)
    plt.legend()
    plt.show()
