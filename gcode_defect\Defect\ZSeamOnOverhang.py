from typing import Any
import numpy as np

from gcode_defect.Defect.Base import DefectAnal<PERSON>zerBase, DefectVisualization, Drawable
from gcode_defect.GcodeParse.ModelInfo import ModelInfo, DotType, TrackInfo, MotionType
from gcode_defect.Defect.Overhang import OverhangAnalyzer

class ZSeamOnOverhangAnalyzer(DefectAnalyzerBase):
    """
    Analyzes and detects Z-seam points that are located on or very close to overhang segments.
    This analyzer reuses the OverhangAnalyzer to first identify all overhang regions.
    """

    @property
    def defect_name(self) -> str:
        return "z_seam_on_overhang"

    @property
    def analysis_params(self) -> list[dict[str, Any]]:
        """Combines parameters from the OverhangAnalyzer with its own specific parameters."""
        # Reuse all parameters from the OverhangAnalyzer
        overhang_analyzer = OverhangAnalyzer()
        params = overhang_analyzer.analysis_params
        
        # Update parameters specific to this analyzer
        for p in params:
            if p['id'] == 'max_overhang_length':
                p['default'] = 5.0
                break
        return params

    from gcode_defect.Defect.Overhang import OverhangSegment
    def analysis(self, model: ModelInfo, precomputed: list[OverhangSegment], **kwargs) -> set[tuple[float, float, float]]:
        """Detects Z-seam points that are located on overhang segments."""
        if not precomputed:
            return set()
        
        all_z_seams = {
            pos for layer in model.layers for pos in layer.get_z_seam_pos_list()
        }

        all_overhang_endpoints = {
            tuple(p) for oh in precomputed for p in (oh.segment_geom.start, oh.segment_geom.end)
        }
        
        defects = all_z_seams.intersection(all_overhang_endpoints)
        
        return defects

    @staticmethod
    def defect_visualization(analysis_result: list[dict]) -> list[DefectVisualization]:
        """
        Generates visualization objects for the detected defects.
        """
        if not analysis_result:
            return []

        vis_by_layer: dict[int, DefectVisualization] = {}

        for defect in analysis_result:
            layer_id = defect['layer_id']
            if layer_id not in vis_by_layer:
                vis_by_layer[layer_id] = DefectVisualization(metadata={'layer_id': layer_id})
            
            seam_point = defect['z_seam_point']
            vis_by_layer[layer_id].drawables.append(Drawable(type='point', geometry=seam_point, style={'color': 'magenta', 's': 80, 'marker': 'P', 'zorder': 20}))

            segment = defect['closest_overhang_segment']
            z = segment['origin_track'].stPos[2]
            segment_as_track = TrackInfo(st_pos=(*segment['start'], z), end_pos=(*segment['end'], z))
            segment_as_track.motionType = MotionType.G1
            vis_by_layer[layer_id].drawables.append(Drawable(type='track', geometry=segment_as_track, style={'color': 'magenta', 'linewidth': 3.5, 'zorder': 19}))

        return list(vis_by_layer.values())
