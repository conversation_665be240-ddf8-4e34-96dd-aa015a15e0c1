# Standard library imports
from collections import defaultdict
from typing import Any, Optional

# Third-party imports
from rtree import index
from shapely.geometry import Point, Polygon
from shapely.ops import unary_union

# Local application imports
from gcode_defect.Defect.Base import DefectAnalyzerBase, DefectVisualization, Drawable
from gcode_defect.GcodeParse.ModelInfo import LayerInfo, LineType, ModelInfo, TrackInfo
from gcode_defect.utils.filters import (
    filter_by_density,
    filter_by_proximity,
    find_sharp_corners_track_contour,
)
from gcode_defect.utils.spatial_indexing import build_rtree_from_geometries


class OverlapAnalyzer(DefectAnalyzerBase):
    @property
    def defect_name(self) -> str:
        return "overlap"
    
    @property
    def analysis_params(self) -> list[dict[str, Any]]:
        """    
            Args:
                detect_radius: Radius of the circle at the corner to collect adjacent tracks.
                adjust_ratio: Circle area proportional to the detect circle area judges if it is an overlap defect.
                use_corner_filter: If True, enables filtering of defects near sharp corners.
                use_density_filter: If True, enables filtering of isolated, non-clustered defects.
                corner_angle_threshold: For the outer wall filter. Defects near outer corners sharper than this will be ignored.
                inner_corner_detection_angle: For inner wall detection. Inner corners sharper than this will be checked for defects.
                corner_distance_threshold: The distance threshold to ignore defects near sharp outer corners.
                corner_look_ahead_dist: The distance to look forward/backward to calculate corner angles, for filleted corners.
                min_cluster_size: The minimum number of points required to form a dense defect cluster.
                cluster_distance: The maximum distance between defect points to be considered a cluster.
        """
        return [
            {'name': '检测半径 (mm)', 'id': 'detect_radius', 'type': 'float', 'default': 1.2},
            {'name': '面积调整系数', 'id': 'adjust_ratio', 'type': 'float', 'default': 0.02},
            {'name': '过滤距离 (mm)', 'id': 'corner_distance_threshold', 'type': 'float', 'default': 1.0},
            {'name': '外墙锐角过滤', 'id': 'use_corner_filter', 'type': 'bool', 'default': True},
            {'name': '外墙过滤角度 (deg)', 'id': 'corner_angle_threshold', 'type': 'int', 'default': 100},
            {'name': '内墙过滤角度 (deg)', 'id': 'inner_corner_detection_angle', 'type': 'int', 'default': 90},         
            {'name': '前瞻距离 (mm)', 'id': 'corner_look_ahead_dist', 'type': 'float', 'default': 1.5},
            {'name': '孤点过滤', 'id': 'use_density_filter', 'type': 'bool', 'default': True},
            {'name': '孤点最小簇大小', 'id': 'min_cluster_size', 'type': 'int', 'default': 3},
            {'name': '孤点最大距离 (mm)', 'id': 'cluster_distance', 'type': 'float', 'default': 3.0}
        ]


    def analysis(self, model: ModelInfo, precomputed: dict[str, Any], **kwargs):
        """
        Detect overlap defects in 3D printing model based on inner wall contours.

        Returns: A list of DefectVisualization objects, each representing one overlap defect.
        """
        params = {p['id']: p['default'] for p in self.analysis_params}
        params.update(kwargs)

        all_potential_defects = []

        # --- STEP 1: Collect all potential defects and apply layer-specific corner filter ---
        for layer_id, layer in enumerate(model.layers):
            # Detect overlaps of inner walls and gap infill within outer wall
            if layer_id % 10 == 0:
                print(f"Layer {layer_id} overlap defect analysis started")
            
            wall_relations = build_wall_relations(layer)
            candidate_tracks = _collect_candidate_tracks_for_layer(layer, wall_relations)
            if not candidate_tracks:
                continue

            # Detect overlaps
            layer_overlaps = detect_inner_wall_overlap(candidate_tracks,
                                                    out_wall_closed_region=get_internal_region(layer, wall_relations),
                                                    wall_relations=wall_relations,
                                                    detect_radius=params['detect_radius'],
                                                    adjust_ratio=params['adjust_ratio'],
                                                    corner_angle_threshold=params['inner_corner_detection_angle'],
                                                    corner_look_ahead_dist=params['corner_look_ahead_dist'])
            if not layer_overlaps:
                continue
            
            # Apply corner filter
            defects_to_add = layer_overlaps
            if params['use_corner_filter']:
                outer_wall_contours = layer.outer_wall_countour_list
                sharp_corners = []
                for outer_wall_contour in outer_wall_contours:
                    pts = find_sharp_corners_track_contour(
                        outer_wall_contour,
                        angle_threshold_deg=params['corner_angle_threshold'],
                        look_ahead_dist=params['corner_look_ahead_dist']
                    )
                    if pts:
                        sharp_corners.extend(pts)
                
                if sharp_corners:
                    # Get the set of points that pass the proximity filter
                    passed_points_set = set(filter_by_proximity(
                        points_to_filter=[d['detect_point'] for d in defects_to_add],
                        reference_points=sharp_corners,
                        distance_threshold=params['corner_distance_threshold']
                    ))
                    defects_to_add = [d for d in defects_to_add if d['detect_point'] in passed_points_set]

            # Add layer_id to each defect and append to the global list
            for defect in defects_to_add:
                defect['layer_id'] = layer_id
                all_potential_defects.append(defect)

        # --- STEP 2: Apply global 3D density filter ---
        if not params['use_density_filter'] or not all_potential_defects:
            final_defects = all_potential_defects
        else:
            # Filter the points based on 3D density
            clustered_points_set = set(filter_by_density(
                points=[d['detect_point'] for d in all_potential_defects],
                min_cluster_size=params['min_cluster_size'],
                max_distance=params['cluster_distance']
            ))
            final_defects = [d for d in all_potential_defects if d['detect_point'] in clustered_points_set]

        return final_defects

    @staticmethod
    def defect_visualization(analysis_result) -> DefectVisualization:
        """Generates DefectVisualization objects from a list of defect data."""
        if not analysis_result:
            return None

        all_defect_visualizations = []
        for defect in analysis_result:
            vis = DefectVisualization(metadata={'layer_id': defect['layer_id'], 'g_code_line': defect['g_code_line_num']})
            vis.drawables.append(Drawable(type='point', geometry=defect['detect_point'], style={'color': 'red', 's': 60, 'marker': 'x', 'zorder': 10}))
            vis.drawables.append(Drawable(type='polygon_2d', geometry=defect['material_region'], style={'facecolor': 'lightgray', 'edgecolor': 'gray', 'alpha': 0.4, 'zorder': 14}))
            vis.drawables.append(Drawable(type='polygon_2d', geometry=defect['overlap_region'], style={'facecolor': 'red', 'edgecolor': 'darkred', 'alpha': 0.6, 'zorder': 15}))
            vis.drawables.append(Drawable(type='polygon_2d', geometry=defect['detect_region'], style={'facecolor': 'none', 'edgecolor': 'purple', 'alpha': 0.8, 'zorder': 16}))
            annotation_text = f"V_ovlp = {defect['overlap_volume']:.3f} mm^3"
            text_pos = (defect['detect_point'][0], defect['detect_point'][1] + 0.5)
            vis.drawables.append(Drawable(type='text_annotation', geometry=text_pos, text=annotation_text, style={'fontsize': 8, 'ha': 'center', 'bbox': dict(facecolor='white', edgecolor='purple', pad=0.3), 'zorder': 17}))
            all_defect_visualizations.append(vis)
        return all_defect_visualizations

def _collect_candidate_tracks_for_layer(layer: 'LayerInfo', wall_relations: dict[str, Any]) -> list[dict]:
    """Collects inner wall and gap infill tracks for overlap detection in a single layer."""
    candidate_tracks = []

    # Add inner wall tracks from wall_relations records
    inner_walls = [r for r in wall_relations.get('records', []) if r['type'] == 'inner']
    for wall_rec in inner_walls:
        for track in wall_rec['contour']:
            polygon = track.get_track_width_polygon(use_equivalent=True)
            if polygon and polygon.is_valid and not polygon.is_empty:
                candidate_tracks.append({
                    'track': track,
                    'polygon': polygon,
                    'wall_id': wall_rec['id']
                })

    # Add infill tracks that can cause overlaps (optional)
    infill_types_to_check = [LineType.Gap_infill]
    for infill_type in infill_types_to_check:
        infill_list = layer.extract_one_lineType(infill_type)
        for track in infill_list:
            polygon = track.get_track_width_polygon(use_equivalent=True)
            if polygon and polygon.is_valid and not polygon.is_empty:
                candidate_tracks.append({
                    'track': track,
                    'polygon': polygon,
                    'wall_id': f"{infill_type.name}_0"
                })
    return candidate_tracks


def _get_constrained_detect_region(detect_point: tuple[float, float, float],
                                   track_dict: dict,
                                   detect_radius: float,
                                   wall_relations: dict[str, Any],
                                   out_wall_closed_region: dict[str, Polygon]) -> Optional[Polygon]:
    """
    Finds the detection area by intersecting a circle with the correct OUTER wall closed region.

    Args:
        detect_point: The (x, y, z) coordinate of the corner being checked.
        track_dict: The dictionary for the track whose corner is being checked.
        detect_radius: The radius of the detection circle.
        wall_relations: Relations dict containing id_to_parent and id_to_type mappings.
        out_wall_closed_region: Mapping of OUTER wall IDs to their closed Polygon objects.

    Returns:
        A Shapely Polygon representing the valid detection area, or None if not found.
    """

    def _find_parent_outer_wall(child_wall_id: str, relations: dict[str, Any]) -> Optional[str]:
        id_to_parent = relations.get('id_to_parent', {})
        id_to_type = relations.get('id_to_type', {})
        current_id = child_wall_id
        for _ in range(len(id_to_parent) + 5):
            parent_id = id_to_parent.get(current_id)
            if parent_id is None:
                # If this is an OUTER itself, allow it; otherwise, fail
                return current_id if id_to_type.get(current_id) == 'outer' else None
            if id_to_type.get(parent_id) == 'outer':
                return parent_id
            current_id = parent_id
        return None

    out_wall_id = _find_parent_outer_wall(track_dict['wall_id'], wall_relations)
    if not out_wall_id:
        return None

    # Get the closed region for the outer wall
    constrained_region = out_wall_closed_region.get(out_wall_id)
    if not constrained_region:
        return None

    # Create the detection circle and intersect it with the outer wall region
    detect_circle = Point(detect_point[:2]).buffer(detect_radius)
    constrained_detect_region = detect_circle.intersection(constrained_region)

    if constrained_detect_region and not constrained_detect_region.is_empty:
        return constrained_detect_region
    else:
        return None


def _check_point_for_overlap(detect_point: tuple[float, float, float],
                             track_dict: dict,
                             all_tracks_dict: list[dict],
                             rtree_idx: index.Index,
                             wall_relations: dict[str, Any],
                             out_wall_closed_region: dict[str, Polygon],
                             detect_radius: float,
                             adjust_ratio: float) -> tuple[Optional[dict], set]:
    """
    Checks a single point for a significant overlap defect.

    Returns:
        A tuple containing:
        - A dictionary with defect details if a significant overlap is found, otherwise None.
        - A set of (x, y) tuples for nearby track endpoints that fall within the detection area.
    """
    constrained_detect_region = _get_constrained_detect_region(
        detect_point, track_dict, detect_radius, wall_relations, out_wall_closed_region
    )
    if not constrained_detect_region:
        return None, set()

    # Find candidate tracks and calculate intersections within the constrained region
    candidate_indices = list(rtree_idx.intersection(constrained_detect_region.bounds))
    polygons_in_region = []
    newly_detected_positions = set()

    for j in candidate_indices:
        candidate_polygon = all_tracks_dict[j]['polygon']
        intersection = constrained_detect_region.intersection(candidate_polygon)
        if intersection and not intersection.is_empty:
            polygons_in_region.append(intersection)

            # Mark endpoints of this nearby track if they fall within the constrained region
            other_track = all_tracks_dict[j]['track']
            if constrained_detect_region.contains(Point(other_track.stPos[:2])):
                newly_detected_positions.add(other_track.stPos[:2])
            if constrained_detect_region.contains(Point(other_track.endPos[:2])):
                newly_detected_positions.add(other_track.endPos[:2])

    if len(polygons_in_region) < 2:
        return None, newly_detected_positions

    # Calculate overlap and material regions to check for significance
    overlap_union, all_overlaps = get_union_of_intersections(polygons_in_region)
    if not overlap_union or overlap_union.is_empty:
        return None, newly_detected_positions

    material_in_region = unary_union(polygons_in_region)
    overlap_area = sum(o.area for o in all_overlaps)
    empty_area = constrained_detect_region.area - material_in_region.area

    if overlap_area > empty_area + constrained_detect_region.area * adjust_ratio:
        defect_info = {
            'overlap_region': overlap_union,
            'detect_point': detect_point,
            'detect_region': constrained_detect_region,
            'material_region': material_in_region,
            'overlap_volume': overlap_area * track_dict['track'].height,
            'g_code_line_num': track_dict['track'].codeLineNum,
            'wall_id': track_dict.get('wall_id')
        }
        return defect_info, newly_detected_positions

    return None, newly_detected_positions


def detect_inner_wall_overlap(detect_tracks_dict: list[dict],
                              wall_relations: dict[str, Any],
                              out_wall_closed_region: dict[str, Polygon],
                              **kwargs
                              ) -> list[dict]:
    """
    Detect every contour corner overlap within a circle area constrained by outer wall boundaries.

    Args:
        detect_tracks_dict: list of dictionaries, each containing a track, its polygon, and wall_id.
        wall_relations_gdf: A GeoDataFrame containing wall geometries and their parent-child relationships.
        out_wall_closed_region: Mapping of outer wall IDs to their closed Polygon objects.

    Returns:
        A list of dictionaries, each representing a potential overlap defect.
    """
    detect_radius = kwargs['detect_radius']
    adjust_ratio = kwargs['adjust_ratio']
    corner_angle_threshold = kwargs['corner_angle_threshold']
    corner_look_ahead_dist = kwargs['corner_look_ahead_dist']

    overlaps = []
    detected_pos = set()

    polygons = [track_dict['polygon'] for track_dict in detect_tracks_dict]
    rtree_idx = build_rtree_from_geometries(polygons)
    # --- Identify sharp corners of inner walls to use as detection points ---
    inner_wall_contours = defaultdict(list)
    for track_dict in detect_tracks_dict:
        if track_dict['track'].lineType == LineType.Inner_wall:
            inner_wall_contours[track_dict['wall_id']].append(track_dict)

    detection_points_info = []
    if inner_wall_contours:
        # Extract just the TrackInfo objects for the filter function
        contours_for_filter = [[td['track'] for td in c] for c in inner_wall_contours.values()]
        
        sharp_corners_2d = []
        for contour in contours_for_filter:
            pts = find_sharp_corners_track_contour(
                contour,
                angle_threshold_deg=corner_angle_threshold,
                look_ahead_dist=corner_look_ahead_dist
            )
            sharp_corners_2d.extend(pts)
        sharp_corners_set = set(sharp_corners_2d)

        # Map the 2D corner points back to their original 3D points and track_dicts
        for contour_dicts in inner_wall_contours.values():
            for track_dict in contour_dicts:
                track = track_dict['track']
                if track.stPos[:2] in sharp_corners_set:
                    detection_points_info.append({'point': track.stPos, 'track_dict': track_dict})
                if track.endPos[:2] in sharp_corners_set:
                    detection_points_info.append({'point': track.endPos, 'track_dict': track_dict})

    # --- Process each identified sharp corner for overlaps ---
    for info in detection_points_info:
        detect_point = info['point']
        track_dict = info['track_dict']

        if detect_point[:2] in detected_pos:
            continue

        defect, newly_detected = _check_point_for_overlap(
            detect_point, track_dict, detect_tracks_dict, rtree_idx,
            wall_relations, out_wall_closed_region,
            detect_radius, adjust_ratio
        )

        detected_pos.add(detect_point[:2])
        detected_pos.update(newly_detected)

        if defect:
            overlaps.append(defect)

    return overlaps


def get_union_of_intersections(polygons: list[Polygon]) -> tuple[Optional[Polygon], list[Polygon]]:
    """
    Get the union of all pairwise intersections between polygons.
    
    Args:
        polygons: list of shapely Polygon objects
        
    Returns:
        Union of all intersection areas, or None if no intersections exist
    """
    intersections = []
    
    # Collect all pairwise intersections
    for i in range(len(polygons)):
        for j in range(i + 1, len(polygons)):
            intersection = polygons[i].intersection(polygons[j])  # intersection is a polygon
            if intersection and not intersection.is_empty:
                intersections.append(intersection)
    
    if not intersections:
        return None, []
    
    # Return union of all intersections
    return unary_union(intersections), intersections


def build_wall_relations(layer: 'LayerInfo') -> dict[str, Any]:
    """
    Build wall polygons and determine parent relationships using Shapely STRtree.
    Returns a relations dict with:
      - records: list of {id, type, geometry, contour, area, parent_id}
      - id_to_parent: dict[id -> parent_id or None]
      - id_to_type: dict[id -> type]
    """
    # Cache on layer
    if hasattr(layer, 'wall_relations'):
        return layer.wall_relations

    if not hasattr(layer, 'outer_wall_countour_list'):
        layer.extract_outer_inner_wall_contour()

    from gcode_defect.utils.poly import build_closed_tracks_polygon

    # 1) Build records for all walls
    records: list[dict[str, Any]] = []
    wall_types = [
        (layer.outer_wall_countour_list, 'outer'),
        (layer.inner_wall_countour_list, 'inner'),
        (layer.overhang_wall_countour_list, 'overhang')
    ]
    for contour_list, wall_type in wall_types:
        for i, contour in enumerate(contour_list):
            poly = build_closed_tracks_polygon(contour)
            if poly:
                records.append({
                    'id': f'{wall_type}_{i}',
                    'type': wall_type,
                    'geometry': poly,
                    'contour': contour,
                    'area': poly.area
                })

    if not records:
        layer.wall_relations = {'records': [], 'id_to_parent': {}, 'id_to_type': {}}
        return layer.wall_relations

    # 2) Build R-tree (rtree) for spatial parent lookup using bounding boxes
    rtree_idx = index.Index()
    for i, rec in enumerate(records):
        rtree_idx.insert(i, rec['geometry'].bounds)

    # 3) Determine immediate parent: smallest-area polygon that COVERS this one (excluding self)
    id_to_parent: dict[str, Optional[str]] = {}
    for i, rec in enumerate(records):
        geom = rec['geometry']
        candidate_indices = list(rtree_idx.intersection(geom.bounds))
        parent_candidates = []
        for j in candidate_indices:
            if j == i:
                continue
            if records[j]['geometry'].covers(geom):
                parent_candidates.append(records[j])
        if parent_candidates:
            parent = min(parent_candidates, key=lambda r: r['area'])
            id_to_parent[rec['id']] = parent['id']
        else:
            id_to_parent[rec['id']] = None

    # 4) Package relations and cache
    id_to_type = {r['id']: r['type'] for r in records}
    relations = {
        'records': records,
        'id_to_parent': id_to_parent,
        'id_to_type': id_to_type,
    }
    layer.wall_relations = relations
    return relations


def get_internal_region(layer: 'LayerInfo', wall_relations: dict[str, Any]) -> dict[str, Polygon]:
    """
    Build ring regions formed by adjacent OUTER walls, using Shapely only.
    For each outer wall O, shrink it inward by half its width, then subtract the union of its
    immediate OUTER descendants (i.e., the next inner OUTER walls directly under O in the
    OUTER-only hierarchy), each buffered outward by half its width.
    The innermost OUTER (with no OUTER child) yields its own shrunken island.
    """
    # Check cache
    if hasattr(layer, 'outer_wall_closed_region'):
        return layer.outer_wall_closed_region

    records = wall_relations.get('records', [])
    if not records:
        layer.outer_wall_closed_region = {}
        return layer.outer_wall_closed_region

    id_to_parent = wall_relations['id_to_parent']
    id_to_type = wall_relations['id_to_type']

    # Collect OUTER ids and fast lookup for their records
    outer_ids = [r['id'] for r in records if r['type'] == 'outer']
    outer_by_id = {r['id']: r for r in records if r['type'] == 'outer'}

    # Helper: nearest OUTER parent by climbing through non-outer nodes
    def nearest_outer_parent(oid: str) -> Optional[str]:
        cur = id_to_parent.get(oid)
        for _ in range(len(id_to_parent) + 5):
            if cur is None:
                return None
            if id_to_type.get(cur) == 'outer':
                return cur
            cur = id_to_parent.get(cur)
        return None

    # Build OUTER adjacency: parent OUTER -> list of child OUTER ids
    from collections import defaultdict
    outer_children = defaultdict(list)
    for oid in outer_ids:
        p_outer = nearest_outer_parent(oid)
        if p_outer is not None:
            outer_children[p_outer].append(oid)

    # Compute ring regions per OUTER
    closed_regions: dict[str, Polygon] = {}
    for oid in outer_ids:
        orec = outer_by_id[oid]
        width_offset = orec['contour'][0].get_equivalent_rectangle_width() / 2
        island = orec['geometry'].buffer(-width_offset)

        child_ids = outer_children.get(oid, [])
        if child_ids:
            child_buffers = []
            for cid in child_ids:
                crec = outer_by_id.get(cid)
                if not crec:
                    continue
                c_width = crec['contour'][0].get_equivalent_rectangle_width() / 2
                child_buffers.append(crec['geometry'].buffer(c_width))
            if child_buffers:
                all_children = unary_union(child_buffers)
                island = island.difference(all_children)

        if island and not island.is_empty:
            closed_regions[oid] = island

    layer.outer_wall_closed_region = closed_regions
    return closed_regions
