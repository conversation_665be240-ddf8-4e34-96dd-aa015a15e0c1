import unittest
import numpy as np
from gcode_defect.utils.filters import find_sharp_corners_track_contour
from gcode_defect.GcodeParse.ModelInfo import MotionType


class MockTrackInfo:
    """A mock class for TrackInfo to be used in tests, simulating G-code tracks."""

    def __init__(self, stPos, endPos, motionType=MotionType.G1, arc_samples=None):
        self.stPos = stPos
        self.endPos = endPos
        self.motionType = motionType
        # Ensure arc_samples is a list of 3D points if provided
        self._arc_samples = [p if len(p) == 3 else (*p, 0.0) for p in arc_samples] if arc_samples else []

    def get_arc_sample_paras(self):
        """Mocks the method that returns sampled points for an arc."""
        if self.motionType in [MotionType.G2, MotionType.G3]:
            return {'sample_points': self._arc_samples}
        return {'sample_points': []}


class TestFindSharpCorners(unittest.TestCase):
    """Unit tests for the find_sharp_corners_track_contour function."""

    def assertPointlistsAlmostEqual(self, list1, list2, places=7, msg=None):
        """
        Asserts that two lists of (x, y) points are equal, ignoring order
        and allowing for floating point inaccuracies.
        """
        if list1 is None:
            list1 = []
        if list2 is None:
            list2 = []

        self.assertEqual(len(list1), len(list2),
                         f"lists have different lengths: {len(list1)} vs {len(list2)}. {msg or ''}")

        # Sort lists to ensure comparison is order-independent
        sorted_list1 = sorted(list1)
        sorted_list2 = sorted(list2)

        for p1, p2 in zip(sorted_list1, sorted_list2):
            self.assertAlmostEqual(p1[0], p2[0], places=places, msg=msg)
            self.assertAlmostEqual(p1[1], p2[1], places=places, msg=msg)

    def test_empty_and_short_contours(self):
        """Test with empty or too-short contours, which should return no corners."""
        self.assertEqual(find_sharp_corners_track_contour([], 90), [], "Empty contour should result in an empty list")

        contour_one_track = [MockTrackInfo((0, 0, 0), (10, 0, 0))]
        self.assertEqual(find_sharp_corners_track_contour(contour_one_track, 90), [], "Contour with 2 points should result in an empty list")

    def test_straight_line_open_contour(self):
        """Test a straight line, which has no corners (all angles are 180 deg)."""
        contour = [
            MockTrackInfo((0, 0, 0), (10, 0, 0)),
            MockTrackInfo((10, 0, 0), (20, 0, 0))
        ]
        corners = find_sharp_corners_track_contour(contour, 170)
        self.assertEqual(corners, [], "A straight line should have no sharp corners")

    def test_simple_square_closed_contour_and_threshold(self):
        """Test a simple closed square and check thresholding logic."""
        contour = [
            MockTrackInfo((0, 0, 0), (10, 0, 0)),
            MockTrackInfo((10, 0, 0), (10, 10, 0)),
            MockTrackInfo((10, 10, 0), (0, 10, 0)),
            MockTrackInfo((0, 10, 0), (0, 0, 0)),
        ]
        expected_corners = [(10.0, 0.0), (10.0, 10.0), (0.0, 10.0), (0.0, 0.0)]

        # A 91-degree threshold should find the 90-degree corners
        corners = find_sharp_corners_track_contour(contour, 91, look_ahead_dist=1.0)
        self.assertPointlistsAlmostEqual(corners, expected_corners, msg="Should find all 4 corners of the square")

        # An 89-degree threshold should find no corners
        corners_strict = find_sharp_corners_track_contour(contour, 89, look_ahead_dist=1.0)
        self.assertEqual(corners_strict, [], "Should find no corners with a strict threshold")

    def test_simple_zigzag_open_contour(self):
        """Test an open zig-zag line, which should not check endpoints."""
        contour = [
            MockTrackInfo((0, 0, 0), (10, 10, 0)),
            MockTrackInfo((10, 10, 0), (20, 0, 0)),
            MockTrackInfo((20, 0, 0), (30, 10, 0)),
        ]
        # Open contours don't check endpoints (0,0) and (30,10).
        # Should find the two inner corners at (10,10) and (20,0).
        expected_corners = [(10.0, 10.0), (20.0, 0.0)]
        corners = find_sharp_corners_track_contour(contour, 100, look_ahead_dist=2.0)
        self.assertPointlistsAlmostEqual(corners, expected_corners, msg="Should find two inner corners")

    def test_filleted_corner_with_look_ahead(self):
        """Test that a large look_ahead_dist can correctly identify a filleted corner."""
        # A 90-degree corner at (10,0) is replaced with a small fillet.
        contour = [
            MockTrackInfo((0, 0, 0), (9, 0, 0)),
            MockTrackInfo((9, 0, 0), (9.707, 0.293, 0)),  # Point on the fillet
            MockTrackInfo((9.707, 0.293, 0), (10, 1, 0)),
            MockTrackInfo((10, 1, 0), (10, 10, 0))
        ]

        # With a small look_ahead, the curve appears smooth, so no corner is detected.
        corners_small_lookahead = find_sharp_corners_track_contour(contour, 110, look_ahead_dist=0.5)
        self.assertEqual(corners_small_lookahead, [], "Should not find corner with small look_ahead")

        # With a large look_ahead, the function "sees" past the fillet and detects the overall turn.
        # The point identified as the corner will be one of the vertices on the fillet itself.
        corners_large_lookahead = find_sharp_corners_track_contour(contour, 110, look_ahead_dist=2.0)
        expected_corner = [(9.707, 0.293)]
        self.assertPointlistsAlmostEqual(corners_large_lookahead, expected_corner,
                                         msg="Should find the filleted corner with large look_ahead")

    def test_sharp_arc_transition(self):
        """Test a sharp, non-tangential transition from a line to an arc."""
        # Path: (0,0) -> (10,0), then a G3 arc that starts non-tangentially.
        # The incoming line has tangent (1,0).
        # The arc starts at (10,0) with center (11,-1), so its initial tangent is (1,1).
        # This creates a 135-degree angle (a 45-degree turn) at the junction.
        arc_start = (10.0, 0.0, 0.0)
        arc_end = (10.0, -2.0, 0.0)
        # Sample point on the arc.
        arc_sample_point = (11 - np.sqrt(2), -1.0, 0.0)  # approx (9.586, -1.0, 0.0)

        contour = [
            MockTrackInfo((0, 0, 0), arc_start, motionType=MotionType.G1),
            MockTrackInfo(arc_start, arc_end, motionType=MotionType.G3, arc_samples=[arc_sample_point])
        ]

        expected_corners = [(10.0, 0.0)]
        # The angle is 135 degrees, so a threshold of 140 should find it.
        corners = find_sharp_corners_track_contour(contour, 140, look_ahead_dist=1.0)
        self.assertPointlistsAlmostEqual(corners, expected_corners,
                                         msg="Should find the sharp corner at the non-tangential line-to-arc transition")

    def test_short_segment_less_than_lookahead(self):
        """Test behavior when a segment is shorter than the look_ahead_dist."""
        # Path with a very short segment between two long ones.
        # The function should still correctly identify the corner region.
        contour = [
            MockTrackInfo((0, 0, 0), (10, 0, 0)),
            MockTrackInfo((10, 0, 0), (10.1, 0, 0)),  # Very short segment
            MockTrackInfo((10.1, 0, 0), (10.1, 10, 0))
        ]

        # The angle at (10,0) is 180. The angle at (10.1, 0) is 90.
        corners = find_sharp_corners_track_contour(contour, 95, look_ahead_dist=2.0)
        expected_corners = [(10.0, 0.0), (10.1, 0.0)]
        self.assertPointlistsAlmostEqual(corners, expected_corners, "Should find both points in corner region with large look_ahead")

    def test_collinear_points_in_path(self):
        """Test a path with multiple collinear points, which should not be corners."""
        contour = [
            MockTrackInfo((0, 0, 0), (10, 0, 0)),
            MockTrackInfo((10, 0, 0), (20, 0, 0)),
            MockTrackInfo((20, 0, 0), (30, 0, 0))
        ]
        corners = find_sharp_corners_track_contour(contour, 179)
        self.assertEqual(corners, [], "Collinear points should not be identified as corners")

    def test_closed_triangle_contour(self):
        """Test a closed loop with multiple sharp points to verify wrap-around logic."""
        # A simple triangle. Angles are: 135 deg at (10,0), 90 deg at (5,5), and 135 deg at (0,0).
        contour = [
            MockTrackInfo((0, 0, 0), (10, 0, 0)),
            MockTrackInfo((10, 0, 0), (5, 5, 0)),
            MockTrackInfo((5, 5, 0), (0.05, 0.05, 0))
        ]
        expected_corners = [(10.0, 0.0), (5.0, 5.0), (0.0, 0.0)]
        corners = find_sharp_corners_track_contour(contour, 140, look_ahead_dist=2.0)
        self.assertPointlistsAlmostEqual(corners, expected_corners, "Should find all 3 corners of a closed triangle")

        # Test with a stricter threshold that only includes the 90-degree corner.
        corners_strict = find_sharp_corners_track_contour(contour, 60, look_ahead_dist=2.0)
        expected_strict = [(10.0, 0.0), (0.0, 0.0)]
        self.assertPointlistsAlmostEqual(corners_strict, expected_strict,
                                         "Should find only the 90-degree corner with a strict threshold")


if __name__ == '__main__':
    unittest.main()