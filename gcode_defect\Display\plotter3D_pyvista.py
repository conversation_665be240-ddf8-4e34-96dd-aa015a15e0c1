import pyvista as pv
import numpy as np
from gcode_defect.GcodeParse.ModelInfo import MotionType, LineType, TrackInfo
from gcode_defect.Display.plotter_base import PlotterBase, color_mode, LineColor_MAP, color_to_rgb
from gcode_defect.Defect.Base import DefectVisualization
from pyvistaqt import QtInteractor
from collections import defaultdict

class Plotter3DPyVista(PlotterBase):    
    def __init__(self, model = None):
        super().__init__(model)
        self.plotter = None
        self.qt_widget = None
        self.current_color_mode = None
        self.current_defects = None

        # --- State for rendering and updates ---
        self._current_alpha = 1.0
        self._current_line_width = 1.0
        self.batched_tracks = defaultdict(list)

        # --- Geometry and Actor Management ---
        self.blocks = pv.MultiBlock()
        self.block_types = []
        self._actors_by_type = {}

        # --- Defect Geometry and Actor Management ---
        self.defect_actors = []
        self.scalar_bar_actor = None

    # --- Widget Setup and Teardown ---
    def init_widget(self, parent_widget):
        """
        Robustly initialize the PyVista widget within a parent QWidget.
        This method is idempotent and safely handles layout management.
        """
        self.init_widget_layout(parent_widget)

        # Create the PyVista widget, which is both the plotter and the QWidget
        self.plotter = QtInteractor(parent_widget)
        self.qt_widget = self.plotter

        # Add the new widget to the prepared layout
        parent_widget.layout().addWidget(self.qt_widget)
        
        # Setup 3D scene with axes and labels
        self.setup_3d_scene()

    def setup_3d_scene(self):
        """Setup 3D scene with safe settings"""
        try:
            # Basic scene setup without risky modifications
            self.plotter.set_background('white')
            
            # Only add safe global settings
            if hasattr(self.plotter, 'renderer'):
                renderer = self.plotter.renderer
                # Safe renderer modifications only
                try:
                    if hasattr(renderer, 'SetUseLODActor'):
                        renderer.SetUseLODActor(False)
                except Exception as e:
                    print(f"Warning: Could not disable LOD actor: {e}")
                
        except Exception as e:
            print(f"Error in setup_3d_scene: {e}")
            # Continue with minimal setup
            try:
                self.plotter.set_background('white')
            except:
                pass

    def _clear_defects(self):
        """Removes only the defect actors from the plotter and clears defect data."""
        if not self.plotter:
            return

        # Hide all cached defect actors for fast toggle off
        if hasattr(self, '_defect_actor_cache'):
            for actor in self._defect_actor_cache.values():
                actor.SetVisibility(False)

        # Remove scalar bar actor if it exists
        if self.scalar_bar_actor:
            self.plotter.remove_actor(self.scalar_bar_actor)
            self.scalar_bar_actor = None

    def clear_canvas(self):
        """Clear the 3D canvas"""
        if self.plotter:
            self.plotter.clear_actors()
            self.blocks.clear()
            self.block_types.clear()
            self._actors_by_type.clear()
            # Fully clear defects and their caches since actors were removed from the scene
            self._clear_defects()
            if hasattr(self, '_defect_actor_cache'):
                self._defect_actor_cache.clear()
            self.defect_actors = []
            self.setup_3d_scene()

    def close(self):
        """
        Safely closes the PyVista QtInteractor to release its underlying
        VTK and OpenGL resources. This is crucial to call before the
        application exits to prevent cleanup errors like 'wglMakeCurrent failed'.
        """
        if self.plotter:
            print("Closing PyVista QtInteractor to release OpenGL context.")
            try:
                # The close method of QtInteractor handles the cleanup of the render window
                self.plotter.close()
            except Exception as e:
                print(f"Error while closing the PyVista plotter: {e}")
            finally:
                self.plotter = None
                self.qt_widget = None

    # --- Public Plotting API ---
    def plot_one_layer(self, clr_md=color_mode.line_type_based, alpha=1.0, line_width=1.0, is_clear_previous = True,
                       defects: list[DefectVisualization] = None):
        """Clears the scene and renders a single layer of the model."""
        if self.model is None:
            raise ValueError("Model not set")
        if self.plotter is None:
            raise ValueError("3D plotter not initialized")
        if not (0 <= self.layer_id < len(self.model.layers)):
            raise ValueError("Invalid layer_id")
        if is_clear_previous:
            self.clear_canvas()

        tracks = self.model.layers[self.layer_id].tracks
        self._render(tracks, clr_md, alpha, line_width, defects=defects)
        print(f"✅ Rendered layer {self.layer_id}.")

    def plot_model(self, clr_md=color_mode.line_type_based, alpha=1.0, line_width=1.0, defects: list[DefectVisualization] = None):
        """Clears the scene and renders the entire 3D model."""
        if self.model is None:
            raise ValueError("Model not set")
        if self.plotter is None:
            raise ValueError("3D plotter not initialized")
        self.clear_canvas()

        tracks_to_render = [t for layer in self.model.layers for t in layer.tracks]
        
        # Performance optimization for very large models
        if len(tracks_to_render) > 250000:
            print(f"Model has {len(tracks_to_render)} tracks, which is large. Plotting only essential walls for performance.")
            types_to_plot = {LineType.Outer_wall, LineType.Inner_wall, LineType.Overhang_wall, LineType.Gap_infill, LineType.Support, LineType.Support_interface}
            tracks_to_render = [t for t in tracks_to_render if t.lineType in types_to_plot]
            print(f"Rendering {len(tracks_to_render)} essential tracks.")

        self._render(tracks_to_render, clr_md, alpha, line_width, defects=defects)
        print(f"✅ Rendered model.")

    # --- Internal Rendering Pipeline ---
    def _batch_tracks_by_type(self, tracks: list[TrackInfo]) -> dict:
        """Batch tracks by type for efficient rendering"""
        self.batched_tracks = defaultdict(list)
        for track in tracks:
            t_type = track.lineType if track.lineType is not None else track.dotType
            self.batched_tracks[t_type].append(track)
        return self.batched_tracks

    def _render(self, tracks_to_process: list[TrackInfo], clr_md, alpha, line_width, defects: list[DefectVisualization] = None):
        """
        Core rendering pipeline that orchestrates the entire process of plotting tracks.
        1. Clears the canvas.
        2. Prepares and batches data.
        3. Builds geometry.
        4. Creates actors and adds them to the scene.
        """
        # Store state for potential updates (e.g., color changes)
        self._current_alpha = alpha
        self._current_line_width = line_width
        self.current_color_mode = clr_md
        self.current_defects = defects

        # --- 1. Data Preparation ---
        self.ensure_filter_map_initialized()
        self.calculate_extreme_values(tracks_to_process, clr_md)
        self.batched_tracks = self._batch_tracks_by_type(tracks_to_process)

        # --- 2. Geometry Creation ---
        self._build_geometry_blocks(self.batched_tracks, clr_md, defects=defects)

        # --- 3. Actor Creation & Rendering ---
        self._create_actors_from_blocks(alpha, line_width)
        self.plotter.show_axes()
        self.plotter.reset_camera()
        self.plotter.render()

    def _create_actors_from_blocks(self, alpha: float, line_width: float):
        """Iterates through geometry blocks, creates an actor for each, and adds it to the plotter."""
        from gcode_defect.GcodeParse.ModelInfo import DotType

        for i, block in enumerate(self.blocks):
            if i >= len(self.block_types): continue  # Safety check
            block_type = self.block_types[i]

            # DotTypes are now meshes (spheres), so they need lighting to look 3D.
            # LineTypes generally don't need lighting.
            use_lighting = True if block_type in DotType else False

            actor = self.plotter.add_mesh(
                block,
                scalars='color',
                rgb=True,
                line_width=line_width,  # Ignored for sphere meshes, used for lines
                opacity=alpha,
                pickable=False,
                lighting=use_lighting  # Enable lighting for spheres to give them a 3D look
            )
            self._actors_by_type[block_type] = actor

            # Set initial visibility based on the filter map
            is_visible = self.line_isPlot_MAP.get(block_type, True)
            actor.SetVisibility(is_visible)

    def _generate_arc_points_3d(self, track: TrackInfo):
        """Generate 3D arc points as a numpy array."""
        track.get_arc_sample_paras()
        angles = track.arc_paras['sample_angles_rad']
        center = track.arc_paras['center'][:2]
        radius = track.arc_paras['radius']

        x = center[0] + radius * np.cos(angles)
        y = center[1] + radius * np.sin(angles)
        z = np.linspace(track.stPos[2], track.endPos[2], len(x))
        
        return np.column_stack((x, y, z))

    def _build_geometry_blocks(self, batched_tracks, clr_md, defects: list[DefectVisualization] = None):
        """Add all tracks in the model to the multiblock dataset for rendering."""
        from gcode_defect.GcodeParse.ModelInfo import LineType, DotType, MotionType
        if not self.model:
            return 
        
        for type, tracks in batched_tracks.items():
            new_poly = None # Initialize to handle cases where type is not recognized
            pts = []
            lines = []
            colors = []

            # Get lines, point, and color
            if isinstance(type, LineType):
                pts, lines, colors = self._get_track_mesh_paras(tracks, clr_md, defects=defects)

                if pts:
                    new_poly = pv.PolyData()
                    new_poly.lines = lines
                    new_poly.points = pts
                    new_poly.point_data['color'] = colors

            elif isinstance(type, DotType):
                for track in tracks:
                    track.num_render_points = 1

                points = np.array([t.stPos for t in tracks], dtype=np.float32)
                c = color_to_rgb(self.get_track_color(tracks[0], clr_md))
                colors = [c] * len(points)
                new_poly = self._build_points_sphere(points, colors)

            if new_poly:
                self.blocks.append(new_poly)
                self.block_types.append(type)
    
    def _get_track_mesh_paras(self, tracks: list[TrackInfo], clr_md: color_mode, defects: list[DefectVisualization] = None):
        if not tracks:
            return [], [], []

        pts = []
        lines = []
        colors = []

        defect_track_uids = set()

        for track in tracks:
            c = color_to_rgb(self.get_track_color(track, clr_md))

            if track.motionType == MotionType.G1:
                p = [track.stPos, track.endPos]
                l = [2, len(pts), len(pts) + 1]
            elif track.motionType in [MotionType.G2, MotionType.G3]:
                #arc is separated into many lines
                p = self._generate_arc_points_3d(track)
                l = [len(p)] + list(range(len(pts), len(pts) + len(p)))
            else:
                continue
            
            # Side-effect: Cache point count on the track object.
            # This is a performance optimization to avoid recalculating for color updates.
            track.num_render_points = len(p)
            
            pts.extend(p)
            lines.extend(l)
            colors.extend([c] * track.num_render_points) 

        return pts, lines, colors

    def _build_points_sphere(self, points: list[tuple], colors: list[tuple], dot_size: float = 0.5):
        # Create a PolyData object for the points
        point_cloud = pv.PolyData(points)
        point_cloud.point_data['color'] = colors

        # Create sphere glyphs at each point. The glyphs will inherit the color.
        # The radius is half the desired diameter (dot_size).
        sphere_geom = pv.Sphere(radius=dot_size / 2.0)
        sphere_glyphs = point_cloud.glyph(geom=sphere_geom, scale=False, orient=False)
        return sphere_glyphs

    def _build_points_glyphs(self, points: list, style: dict) -> pv.PolyData:
        """Builds a PolyData object of sphere glyphs for a list of points, styled according to the provided dictionary."""
        point_cloud = pv.PolyData(points)

        # Use 's' from style for size, map to a radius. This mirrors matplotlib's 's' for scatter size.
        # A simple mapping: radius = sqrt(s) / 20. For s=60, radius is ~0.38.
        marker_size = style.get('s', 40)
        radius = np.sqrt(marker_size) / 20.0 if marker_size > 0 else 0.25

        sphere_geom = pv.Sphere(radius=radius)
        glyphs = point_cloud.glyph(geom=sphere_geom, scale=False, orient=False)
        return glyphs

    def _build_track_geometry_for_overlay(self, tracks: list[TrackInfo]):
        """Fast path for building simple line geometry for overlay tracks (defects/highlights)."""
        from gcode_defect.GcodeParse.ModelInfo import MotionType
        pts = []
        lines = []
        for track in tracks:
            if track.motionType == MotionType.G1:
                p = [track.stPos, track.endPos]
                l = [2, len(pts), len(pts) + 1]
            elif track.motionType in [MotionType.G2, MotionType.G3]:
                p = self._generate_arc_points_3d(track)
                l = [len(p)] + list(range(len(pts), len(pts) + len(p)))
            else:
                continue
            pts.extend(p)
            lines.extend(l)
        return pts, lines

    # --- Dynamic Updates ---
    def update_visibility(self):
        """Applies the current visibility settings from self.line_isPlot_MAP to all actors."""
        if not self.plotter:
            return
        for line_type, visible in self.line_isPlot_MAP.items():
            if line_type in self._actors_by_type:
                actor = self._actors_by_type[line_type]
                actor.SetVisibility(visible)
        self.plotter.render()

    def update_color(self, clr_md, defects: list[DefectVisualization] = None):
        """
        Efficiently updates the colors of existing actors without re-creating them.
        This method modifies the color data on the underlying PolyData meshes.
        """
        # Allow update if new defects are provided, even if color mode is the same
        if (clr_md == self.current_color_mode and defects is None) or not self.plotter:
            return
        
        if not self.batched_tracks:
            print("Warning: Cannot update color. No track data available.")
            return

        # Recalculate min/max values for color mapping if the mode requires it (e.g., velocity).
        # This is crucial for the colors to be correct when switching modes.
        all_tracks_in_view = [track for tracks in self.batched_tracks.values() for track in tracks]
        self.calculate_extreme_values(all_tracks_in_view, clr_md)

        print(f"Updating color mode from '{self.current_color_mode}' to '{clr_md}'...")
        self.current_color_mode = clr_md
        self.current_defects = defects

        from gcode_defect.GcodeParse.ModelInfo import LineType, DotType, MotionType

        defect_track_uids = set()

        # We need to find which block corresponds to which type.
        # We can iterate through the original blocks and update their color arrays.
        for i, block in enumerate(self.blocks):
            if i >= len(self.block_types): continue
            
            block_type = self.block_types[i]
            tracks_for_block = self.batched_tracks.get(block_type)

            if not tracks_for_block:
                continue

            if isinstance(block_type, DotType):
                # Special handling for glyphs (spheres), which must be regenerated.
                points = np.array([t.stPos for t in tracks_for_block], dtype=np.float32)
                new_colors_for_dots = []
                for track in tracks_for_block:
                    c = color_to_rgb(self.get_track_color(track, clr_md))
                    new_colors_for_dots.append(c)

                # Re-create the glyph mesh with new colors
                new_glyph_poly = self._build_points_sphere(points, new_colors_for_dots)
                actor = self._actors_by_type.get(block_type)
                if actor:
                    actor.mapper.dataset = new_glyph_poly
                    self.blocks[i] = new_glyph_poly # Update block list for consistency

            else: # It's a LineType, update colors in place
                new_colors = []
                for track in tracks_for_block:
                    if not hasattr(track, 'num_render_points'):
                        print(f"Warning: 'num_render_points' not found on track. Skipping color update for this track.")
                        continue
                    
                    num_points = track.num_render_points
                    c = color_to_rgb(self.get_track_color(track, clr_md))
                    new_colors.extend([c] * num_points)

                # Directly update the color data on the mesh, but only if the count is correct.
                if block.n_points == len(new_colors):
                    block.point_data['color'] = new_colors
                else:
                    print(f"Error: Color array size mismatch for block type {block_type}. "
                          f"Expected {block.n_points}, got {len(new_colors)}. Skipping update for this block.")

        self.plotter.render() # Trigger a re-render to show the new colors.
        print(f"✅ Color mode updated to '{clr_md}'.")

    # --- Add defect plot ---
    def plot_defects(self, vis_data: list[DefectVisualization]):
        """
        Plots defects on top of the existing model by batching geometries with the same style
        for efficient rendering. This method is data-driven and respects the style information
        within each `Drawable`. It clears previous defects before drawing new ones.
        """
        if not self.plotter:
            print("Warning: Plotter not initialized. Cannot plot defects.")
            return

        # Build legend first (remove any previous scalar bar)
        # If no vis data, clear legend and exit
        legend_info = None
        if vis_data:
            for vis in vis_data:
                if vis.metadata and 'legend_info' in vis.metadata:
                    legend_info = vis.metadata['legend_info']
                    break
        # Remove previous scalar bar
        if self.scalar_bar_actor:
            self.plotter.remove_actor(self.scalar_bar_actor)
            self.scalar_bar_actor = None
        if not vis_data:
            self.plotter.render()
            return
        if legend_info:
            self._add_scalar_bar(legend_info)

        # Caching defect overlay actors by style to avoid rebuilding between toggles
        drawables_by_style = defaultdict(list)
        for vis in vis_data:
            for item in vis.drawables:
                if item.type not in ['point', 'track']:
                    continue
                style_key = frozenset(item.style.items()) if item.style else frozenset()
                batch_key = (item.type, style_key)
                drawables_by_style[batch_key].append(item.geometry)

        # Build or update actors per style
        if not hasattr(self, '_defect_actor_cache'):
            self._defect_actor_cache = {}
        # Hide all cached actors by default; will re-show those present this call
        for (_, _), actor in list(self._defect_actor_cache.items()):
            actor.SetVisibility(False)

        for (item_type, style_key), geometries in drawables_by_style.items():
            style = dict(style_key)
            actor = self._defect_actor_cache.get((item_type, style_key))

            if item_type == 'point' and geometries:
                glyphs = self._build_points_glyphs(geometries, style)
                if actor is None:
                    actor = self.plotter.add_mesh(glyphs, color=style.get('color', 'red'), lighting=True, pickable=False)
                    self._defect_actor_cache[(item_type, style_key)] = actor
                    self.defect_actors.append(actor)
                else:
                    actor.mapper.dataset = glyphs
                actor.SetVisibility(True)

            elif item_type == 'track' and geometries:
                pts, lines = self._build_track_geometry_for_overlay(geometries)
                if pts:
                    track_geom = pv.PolyData(pts, lines=lines)
                    if actor is None:
                        actor = self.plotter.add_mesh(track_geom, color=style.get('color', 'red'), line_width=style.get('linewidth', 2.0), lighting=False, pickable=False)
                        self._defect_actor_cache[(item_type, style_key)] = actor
                        self.defect_actors.append(actor)
                    else:
                        actor.mapper.dataset = track_geom
                    actor.SetVisibility(True)

        for vis in vis_data:
            if vis.layer_styles:
                for layer_id, style in vis.layer_styles.items():
                    self._render_layer_highlight(layer_id, style)

        self.plotter.render()

    def _add_scalar_bar(self, legend_info: dict):
        """
        Adds a discrete scalar bar (legend) to the plotter.

        This method is designed to be compatible with older PyVista versions
        that may not support the 'annotations' argument and to avoid potential
        UI interaction issues that can arise from other methods.
        """
        title = legend_info.get('title', 'Value')
        max_val = legend_info.get('max_value', 1.0)
        min_val = legend_info.get('min_value', 0.0)
        color_list = legend_info.get('colormap')

        if not color_list or not self.plotter:
            return

        # 1. Create a custom lookup table for our discrete colors.
        n_colors = len(color_list)
        lookup_table = pv.LookupTable(cmap=color_list, n_values=n_colors)
        lookup_table.scalar_range = (min_val, max_val)
        lookup_table.Build()

        # 2. Create a dummy mapper and assign the lookup table to it.
        dummy_mapper = pv.DataSetMapper()
        dummy_mapper.lookup_table = lookup_table
        dummy_mapper.scalar_range = (min_val, max_val)

        # 3. Add the scalar bar directly to the plotter's renderer.
        self.scalar_bar_actor = self.plotter.add_scalar_bar(
            title=title,
            mapper=dummy_mapper,
            n_labels=n_colors + 1,
            title_font_size=16,
            label_font_size=12,
            color='black',
            position_x=0.85,
            position_y=0.1,
            vertical=True,
            fmt="%.3f",
        )

    def _render_layer_highlight(self, layer_id: int, style: dict):
        """Helper to find and render all tracks for a specific layer with a given style."""
        layer_highlight_tracks = []
        for type, tracks in self.batched_tracks.items():
            if self.line_isPlot_MAP[type]:
                layer_highlight_tracks.extend([t for t in tracks if t.layer_id == layer_id])

        if not layer_highlight_tracks:
            return

        pts, lines = self._build_track_geometry_for_overlay(layer_highlight_tracks)
        if pts:
            layer_geom = pv.PolyData(pts, lines=lines)
            actor = self.plotter.add_mesh(
                layer_geom,
                color=style.get('color', 'red'),
                line_width=2.0,
                pickable=False
            )
            if actor:
                self.defect_actors.append(actor)