from enum import Enum
from typing import Optional
from shapely.geometry import Polygon
import math
import numpy as np
from shapely.ops import unary_union


class LineType(Enum):
    Empty_move = "Empty move"
    Wipe="Wipe"
    Gap_infill = "Gap infill"
    Floating_vertical_shell="Floating vertical shell"
    Internal_solid_infill="Internal solid infill"
    Sparse_infill="Sparse infill"
    Outer_wall="Outer wall"
    Inner_wall="Inner wall"
    Overhang_wall="Overhang wall"
    Bottom_surface="Bottom surface"
    Top_surface = "Top surface"
    Bridge="Bridge"
    Support="Support"
    Support_interface = "Support interface"
    Support_transition="Support transition"
    Brim="Brim"
    Custom="Custom"


class DotType(Enum):
    Withdraw = "Withdraw"
    Seam = "Seam"
    AdjustPara = "AdjustPara"
    Reload = "Reload"


class MotionType(Enum):
    G1 = "G1"
    G2 = "G2"
    G3 = "G3"
    pause = "Pause"


class TrackInfo:
    def __init__(self, st_pos, end_pos = 0, c_pos = None, ex = 0, speed = 0, **kwargs):
        # Geometry
        self.stPos: tuple[float, float, float] = st_pos
        self.endPos: tuple[float, float, float] = end_pos
        self.cicPos: tuple[float, float, float] = c_pos
        self.length = kwargs.get('length') or 0
        self.width_comment = 0
        self.width = kwargs.get('width') or 0
        self.height = kwargs.get('height') or 0
        self.polygon: Polygon = None

        # G-code info
        self.ex = ex
        self.speed = speed
        self.accel = 0
        self.time = None
        self.codeLineNum = 0
        self.layer_id = kwargs.get('layer_id')
        self.lineType: LineType = kwargs.get('lineType')
        self.dotType: DotType = kwargs.get('dotType')
        self.motionType: MotionType = kwargs.get('motionType')
        
        # G2/3 discrete parameters
        self.chord_error_mm = 0.01
        self.max_discrete_num = 50

    def calc_track_length(self):
        if self.motionType == MotionType.G1:
            self.length = np.linalg.norm(np.array(self.stPos) - np.array(self.endPos))
            
        elif self.motionType in [MotionType.G2, MotionType.G3]:
            self.get_arc_paras()
            angle_diff_rad = abs(self.arc_paras['start_angle_rad'] - self.arc_paras['end_angle_rad'])
            self.length = self.arc_paras['radius'] * abs(angle_diff_rad)
            
        elif self.motionType == MotionType.pause:
            self.length = 0

        else:
            raise TypeError("not supported MotionType")

    def get_line_sample_pts(self, discrete_l: float):
        """interpolate points along the line track in a discrete length"""  
        if self.motionType != MotionType.G1:
            return
        
        n_pts = int(np.ceil(self.length / discrete_l)) + 1
        if n_pts <= 2:
            return (self.stPos, self.endPos)
        else:
            pts = np.linspace(self.stPos, self.endPos, n_pts)
            # return tuple([tuple(pt) for pt in pts])
            return pts

    def get_arc_paras(self):
        """Calculate some common used parameters of an arc track."""
        from gcode_defect.utils.line_segment import get_arc_angles_from_center

        if hasattr(self, 'arc_paras'):
            return self.arc_paras
        if not self.motionType in [MotionType.G2, MotionType.G3]:
            raise TypeError("not an arc track")
        
        start = np.array(self.stPos[:2])
        end = np.array(self.endPos[:2])
        center = np.array(self.cicPos[:2])
        clockwise = self.motionType == MotionType.G2

        radius = np.linalg.norm(start - center)
        start_angle, end_angle = get_arc_angles_from_center(start, end, center, clockwise)  
                
        self.arc_paras = {
            'center': center,
            'radius': radius,
            'start_angle_rad': start_angle,
            'end_angle_rad': end_angle,
            'clockwise': clockwise
        }
        return self.arc_paras

    def get_arc_sample_paras(self):
        """
        Sample points along the arc track.
        
        Args:
            chord_error_mm: the maximum allowed error between the arc and the poly-line in mm.
            max_discrete_num: the maximum number of discrete points allowed.
        """
        from gcode_defect.utils.line_segment import get_arc_sample_points

        if hasattr(self, 'arc_paras') and 'sample_angles_rad' in self.arc_paras.keys():
            return self.arc_paras

        start = np.array(self.stPos)
        end = np.array(self.endPos)
        center = np.array(self.cicPos)

        # Sample arc at a certain chord_error
        sample_angles, sample_pts = get_arc_sample_points(start, end, center, self.motionType == MotionType.G2,
                                                           self.chord_error_mm, self.max_discrete_num)       
                
        self.arc_paras.update({
            'sample_angles_rad': sample_angles,
            'sample_points': sample_pts
        })
        return self.arc_paras
 
    def get_track_width_polygon(self, use_equivalent: bool = False) -> Optional[Polygon]:
        """
        Build polygon of track with a certain width
        """
        from gcode_defect.utils.poly import build_line_width_polygon, build_arc_width_polygon

        if self.polygon:
            return self.polygon
        
        if not hasattr(self, 'width') or self.width < 0:
            raise ValueError("Invalid width")
        elif self.width == 0 or self.motionType == MotionType.pause:
            return None
        
        width = self.get_equivalent_rectangle_width() if use_equivalent else self.width

        if self.motionType in [MotionType.G2, MotionType.G3]:
            self.polygon = build_arc_width_polygon(self.stPos, self.endPos, self.cicPos, self.motionType == MotionType.G2, width, 
                                          chord_error_mm=self.chord_error_mm, 
                                          max_discrete_num=self.max_discrete_num)
        elif self.motionType == MotionType.G1:
            self.polygon =  build_line_width_polygon(self.stPos, self.endPos, width)
        else:
            return None
        return self.polygon
 
    def calc_and_set_width(self, filament_flow_ratio = 1.0):
        """Calculate the width of the track based on the flow ratio"""
        calib_ex = self.ex / filament_flow_ratio if filament_flow_ratio is not None else self.ex
        if self.motionType in [MotionType.G1, MotionType.G2, MotionType.G3] and self.ex > 0:                   
            self.width = (calib_ex * math.pi * 0.875 **2 / self.length / self.height) - math.pi * self.height / 4 + self.height                                 
            if (abs(self.width_comment - self.width) / self.width_comment > 0.1):
                self.width = self.width_comment
            # if self.ex < 5e-4 or self.lineType == LineType.Bridge: # error is large for 1. ex is quite samll; 2. lineType is Bridge
            #    self.width = self.width_comment 
        else:
            self.width = 0
        return self.width
        
    def get_equivalent_rectangle_width(self) -> float:
        """Get the width of equivalent rectangle with a eaqual corss section"""
        return self.width - 0.2146 * self.height # V = [self.width - (1 - math.pi/4) * self.height] * self.height
    

class LayerInfo:
    def __init__(self, z=0, h=0):
        self.z = z
        self.height = h
        self.time = 0
        self.tracks: list[TrackInfo] = []
        self.z_seam_pos_list: list[tuple[float, float, float]] = []
        
    def extract_outer_inner_wall_contour(self):
        """Extract outer, inner and overhang wall contours from tracks."""
        wall_countour=[]
        self.outer_wall_countour_list=[]
        self.inner_wall_countour_list=[]
        self.overhang_wall_countour_list=[]
        is_extracting_outer=False
        is_extracting_inner=False
        is_extracting_overhang=False
        
        if not self.tracks:
            return
            
        for track in self.tracks:
            if not is_extracting_outer and not is_extracting_inner and not is_extracting_overhang:
                if track.lineType == LineType.Outer_wall:
                    is_extracting_outer=True
                elif track.lineType == LineType.Inner_wall:
                    is_extracting_inner=True
                elif track.lineType == LineType.Overhang_wall:
                    is_extracting_overhang=True
            # if the countour has inner_wall or outer_wall, it is not totally overhang
            if track.lineType in [LineType.Inner_wall,LineType.Outer_wall, LineType.Overhang_wall]:
                wall_countour.append(track)
                if track.lineType == LineType.Outer_wall:
                    is_extracting_outer=True
                    is_extracting_overhang=False
                elif track.lineType == LineType.Inner_wall:
                    is_extracting_inner=True
                    is_extracting_overhang=False
            if track.lineType in [LineType.Empty_move, LineType.Wipe] or track == self.tracks[-1]: # end of a contour
                if is_extracting_outer:
                    self.outer_wall_countour_list.append(wall_countour)
                elif is_extracting_inner:
                    self.inner_wall_countour_list.append(wall_countour)
                elif is_extracting_overhang:
                    self.overhang_wall_countour_list.append(wall_countour)# totally overhang
                is_extracting_outer=False
                is_extracting_inner=False
                is_extracting_overhang=False
                wall_countour=[]
                    
    def get_z_seam_pos_list(self):
        """Extract z seam from wall contours."""
        if hasattr(self, 'z_seam_pos_list'):
            return self.z_seam_pos_list
        
        def find_countour_closed_point(wall_countour_list: list, z_seam_store: list):
            for wall_countour in wall_countour_list:
                is_closed = np.linalg.norm(np.array(wall_countour[0].stPos) - np.array(wall_countour[-1].endPos)) < 0.2
                if is_closed: 
                    seam_pos = wall_countour[-1].endPos
                    z_seam_store.append(seam_pos)

                    last_track_idx = self.tracks.index(wall_countour[-1])
                    seam_track = TrackInfo(seam_pos, seam_pos)
                    seam_track.codeLineNum = wall_countour[-1].codeLineNum
                    seam_track.dotType = DotType.Seam
                    seam_track.motionType = MotionType.pause
                    seam_track.layer_id = self.tracks[-1].layer_id
                    self.tracks.insert(last_track_idx + 1, seam_track)

        z_seam_outer=[]
        z_seam_inner=[]
        z_seam_overhang=[]
        if not self.outer_wall_countour_list or not self.inner_wall_countour_list or not self.overhang_wall_countour_list:
            self.extract_outer_inner_wall_contour()
        if self.outer_wall_countour_list:
            find_countour_closed_point(self.outer_wall_countour_list, z_seam_outer)
        if self.inner_wall_countour_list:
            find_countour_closed_point(self.inner_wall_countour_list, z_seam_inner)
        if self.overhang_wall_countour_list:
            find_countour_closed_point(self.overhang_wall_countour_list, z_seam_overhang)
        self.z_seam_pos_list = z_seam_outer + z_seam_inner + z_seam_overhang
        return self.z_seam_pos_list

    def extract_one_lineType(self, lineType: LineType):
        lineType_countour_list=[]
        if self.tracks:
            for track in self.tracks:
                if track.lineType == lineType:
                    lineType_countour_list.append(track)
        else:
            raise TypeError("self.tracks is NoneType")
        return lineType_countour_list
    
    def extract_one_dotType(self, dotType: DotType):
        dot_list=[]
        if self.tracks:
            for track in self.tracks:
                if track.dotType == dotType:
                    dot_list.append(track)
        else:
            raise TypeError("self.tracks is NoneType")
        return dot_list

class ModelInfo:
    def __init__ (self):
        self.layers: list[LayerInfo]=[]
        self.paras={}

    def get_one_para(self, para_name: str, filament_id : int = None):
        """Get one parameter from the model"""
        if not filament_id:
            if 'filament' in self.paras.keys():
                if type(self.paras['filament']) is list:
                    filament_id = int(self.paras['filament'][0]) - 1 # Temp resolution, should point to coresponding filament 
                else:
                    filament_id = int(self.paras['filament']) - 1
            else:
                filament_id = 0

        if para_name in self.paras.keys():
            if type(self.paras[para_name]) is list:
                if filament_id < 0 or filament_id >= len(self.paras[para_name]):
                    raise ValueError(f"Invalid filament id: {filament_id}")
                return self.paras[para_name][filament_id]
            else:
                return  self.paras[para_name]

    def store_discrete_data(self):
        """calculate all G2/G3 tracks in model to discrete lines"""
        for layer in self.layers:
            for track in layer.tracks:
                if track.motionType in [MotionType.G2, MotionType.G3]:
                    track.get_arc_sample_paras()

    def update_layer_height(self):
        """Update layer height based on track information"""
        # Update z
        for layer in self.layers:
            for track in layer.tracks:
                if track.ex > 0 and track.length > 0 and layer.z == 0:
                    layer.z = track.stPos[2]
                    break
        
        def has_support(layer):
            for track in layer.tracks:
                if track.lineType and track.lineType in [LineType.Support, LineType.Support_interface, LineType.Support_transition]:
                    return True
            return False
        
        def is_independent_support(layer):
            for track in layer.tracks:
                if track.ex > 0 and track.lineType and track.lineType not in [LineType.Support, LineType.Support_interface, LineType.Support_transition]:
                    return False
            return True

        # Update layer height
        last_has_support_z = last_has_track_z = 0
        has_independent_support = self.get_one_para('independent_support_layer_height')

        if has_independent_support is not None and has_independent_support == False:
            for layer in self.layers:
                    layer.height = layer.z - last_has_track_z
                    last_has_track_z = layer.z
        
        else:
            ## Has independent support layer
            for layer in self.layers:
                if is_independent_support(layer):
                    layer.height = layer.z - last_has_support_z
                    last_has_support_z = layer.z
                else:
                    layer.height = layer.z - last_has_track_z
                    last_has_track_z = layer.z
                    if has_support(layer):
                        last_has_support_z = layer.z

        # Update track height 
        for layer in self.layers:
            for track in layer.tracks:
                track.height = layer.height

    def update_track_width(self):
        """Update track width based on flow ratio"""
        filament_flow_ratio = self.get_one_para('filament_flow_ratio')
        for layer in self.layers:
            for track in layer.tracks:
                track.calc_and_set_width(filament_flow_ratio = filament_flow_ratio)