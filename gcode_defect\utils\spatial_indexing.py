from __future__ import annotations
from typing import Any
import numpy as np
from shapely.geometry import Polygon
from shapely.ops import unary_union
from rtree import index

from .line_segment import LineSegment


def build_rtree_from_geometries(geometries: list[Any]) -> index.Index:
    """
    Builds an R-tree spatial index from a list of geometric objects.
    The objects must have either a `.bounds` property (like Shapely polygons)
    or a `.get_axis_aligned_bounding_box()` method.

    Args:
        geometries: A list of geometric objects.

    Returns:
        An rtree.index.Index object populated with the geometries. The ID
        inserted for each geometry corresponds to its index in the input list.
    """
    rtree_idx = index.Index()
    for i, geom in enumerate(geometries):
        bbox = None
        if hasattr(geom, 'bounds'):
            bbox = geom.bounds
        elif hasattr(geom, 'get_axis_aligned_bounding_box'):
            bbox = geom.get_axis_aligned_bounding_box()

        if bbox:
            rtree_idx.insert(i, bbox)
    return rtree_idx


def build_angle_binned_rtree(lines: list[LineSegment], bin_size_degrees: int = 10) -> dict[int, index.Index]:
    """
    Builds a dictionary of R-trees, where each R-tree (a "bin") contains line
    segments that fall within a specific angle range. This is an optimization
    for finding nearly parallel lines.

    Args:
        lines: A list of LineSegment objects to index.
        bin_size_degrees: The size of each angle bin in degrees.

    Returns:
        A dictionary where keys are angle bins (e.g., 0, 10, 20...) and values
        are the R-tree indices for that bin.
    """
    angle_binned_rtrees = {i: index.Index() for i in range(0, 180, bin_size_degrees)}
    for i, line in enumerate(lines):
        if not hasattr(line, 'length') or line.length <= 1e-9:
            continue

        if not hasattr(line, 'normalized_vector'):
            continue

        angle = np.degrees(np.arctan2(line.normalized_vector[1], line.normalized_vector[0]))
        normalized_angle = angle % 180
        bin_key = int(normalized_angle / bin_size_degrees) * bin_size_degrees

        bbox = line.get_axis_aligned_bounding_box()
        angle_binned_rtrees[bin_key].insert(i, bbox)

    return angle_binned_rtrees


def get_overlap_with_rtree(polygon: Polygon, support_rtree: index.Index, support_polygons: list[Polygon]):
    """
    Calculate the overlap ratio and intersection area between a polygon and a set of support polygons using an R-tree for efficient spatial queries.
    
    Returns:
        overlap_ratio: overlap area / polygon area
        intersection: intersection polygon
        supporting_polygons_union: The union of all support polygons that intersect with the input polygon.
    """
    if not polygon or polygon.is_empty:
        return 0, None, None

    # Find candidate support polygons using R-tree
    candidate_indices = list(support_rtree.intersection(polygon.bounds))
    if not candidate_indices:
        return 0, None, None
    
    # Optimization: Intersect with each candidate individually first, then union the small results.
    intersections = []
    intersecting_support_polygons = []
    for i in candidate_indices:
        # A precise intersection check is needed since the R-tree uses bounding boxes.
        support_poly = support_polygons[i]
        intersection = polygon.intersection(support_poly)
        if not intersection.is_empty:
            intersections.append(intersection)
            intersecting_support_polygons.append(support_poly)

    if not intersections:
        return 0, None, None

    total_intersection = unary_union(intersections)
    supporting_polygons_union = unary_union(intersecting_support_polygons) if intersecting_support_polygons else None

    if not total_intersection.is_empty:
        overlap_ratio = total_intersection.area / polygon.area
        return overlap_ratio, total_intersection, supporting_polygons_union
    else:
        return 0, None, None
