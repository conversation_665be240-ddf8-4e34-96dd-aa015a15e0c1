[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "gcode-defect-analyzer"
version = "0.1.0"
authors = [
  { name="DaiXiong", email="<EMAIL>" }, 
]
description = "A tool to analyze G-code files for 3D printing defects."
readme = "README.md"
requires-python = ">=3.8"
license = "MIT"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
    "Topic :: Scientific/Engineering",
]

dependencies = [
    'scipy',
    'pyvistaqt',
    'pyvista',
    'scikit-learn',
    "shapely",
    "rtree",
    "numpy",
    "matplotlib",
    "PySide6"
]

[project.urls]
"Homepage" = "https://gitlab.bambu-lab.com/systest/g-code-defect"

[project.scripts]
gcode-analyzer = "gcode_defect.main:main"

[tool.setuptools]
# Explicitly list the top-level package. Setuptools will find sub-packages automatically.
packages = ["gcode_defect"]
