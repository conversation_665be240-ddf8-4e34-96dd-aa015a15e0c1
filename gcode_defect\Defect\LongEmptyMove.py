from typing import Any
from gcode_defect.GcodeParse.ModelInfo import ModelInfo, LineType
from gcode_defect.Defect.Base import DefectAnalyzerBase, DefectVisualization, Drawable

class LongEmptyMoveAnalyzer(DefectAnalyzerBase):
    @property
    def defect_name(self) -> str:
        return "long_empty_move"
    
    @property
    def analysis_params(self) -> list[dict[str, Any]]:
        """
        Args:
            min_distance: The minimum length to be considered a long empty move.
        """
        return [
            {'name': '最小距离 (mm)', 'id': 'min_distance', 'type': 'float', 'default': 240}
        ]
    
    def analysis(self, model: ModelInfo, precomputed: dict[str, Any], **kwargs):
        """
        Analyzes the model for long empty moves and returns them as visualization data.
        """
        params = {p['id']: p['default'] for p in self.analysis_params}
        params.update(kwargs)
        
        defect_by_layer = {}
        for layer_id, layer in enumerate(model.layers):
            for track in layer.tracks:
                if track.lineType == LineType.Empty_move and track.length >= params['min_distance']:
                    if layer_id not in defect_by_layer:
                        defect_by_layer[layer_id] = []
                    defect_by_layer[layer_id].append(track)
        return defect_by_layer
    
    @staticmethod
    def defect_visualization(analysis_result):
        vis_by_layer: dict[int, DefectVisualization] = {}
        for layer_id, tracks in analysis_result.items():
            vis_by_layer[layer_id] = DefectVisualization(metadata={'layer_id': layer_id})
            for track in tracks:
                vis_by_layer[layer_id].drawables.append(Drawable(type='track', geometry=track, style={'color': 'red', 'linewidth': 2}))
        return list(vis_by_layer.values())
