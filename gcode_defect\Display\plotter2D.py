import numpy as np
import matplotlib
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qtagg import NavigationToolbar2QT as NavigationToolbar
from matplotlib.collections import LineCollection, PatchCollection
from matplotlib.figure import Figure
from matplotlib.lines import Line2D
from matplotlib.patches import Arc, Circle, Polygon as MplPolygon
from shapely.geometry import GeometryCollection, MultiPolygon, Polygon

# Configure matplotlib for high-quality display
matplotlib.rcParams['figure.dpi'] = 150
matplotlib.rcParams['savefig.dpi'] = 300
matplotlib.rcParams['font.size'] = 10
matplotlib.rcParams['axes.linewidth'] = 0.8

from gcode_defect.Defect.Base import DefectVisualization
from gcode_defect.Display.plotter_base import PlotterBase, color_mode
from gcode_defect.GcodeParse.ModelInfo import MotionType, TrackInfo

class plotter2D(PlotterBase):
    def __init__(self, model = None):
        super().__init__(model)
        self.fig = None
        self.ax = None
        self.canvas = None
        self.toolbar = None
        self._arc_patch_cache = {}
        self._arc_width_polygon_cache = {}


    def init_widget(self, parent_widget):
        """Initialize 2D widget in the shared matplotlib_area widget"""
        self.init_widget_layout(parent_widget)

        # Initialize 2D
        self.fig = Figure(figsize=(12, 9), dpi=50)
        self.canvas = FigureCanvas(self.fig)
        self.toolbar = NavigationToolbar(self.canvas, parent_widget)

        parent_widget.layout().addWidget(self.toolbar)
        parent_widget.layout().addWidget(self.canvas)

        self.ax = self.fig.add_subplot(111)
        self.ax.set_xlim(0, 350)
        self.ax.set_ylim(0, 320)
        self.ax.set_aspect("equal")
        self.ax.set_xlabel("x (mm)")
        self.ax.set_ylabel("y (mm)")

    def clear_canvas(self):
        if self.ax is not None:
            for collection in self.ax.collections[:]:
                collection.remove()
            for line in self.ax.lines[:]:
                line.remove()
            for patch in self.ax.patches[:]:
                patch.remove()
            for text in self.ax.texts[:]:
                text.remove()
        # Clear caches to prevent memory growth across replots
        if hasattr(self, '_arc_patch_cache'):
            self._arc_patch_cache.clear()
        if hasattr(self, '_arc_width_polygon_cache'):
            self._arc_width_polygon_cache.clear()
        self.plt_layer_num = -1
        self.canvas.draw_idle()

    def plot_one_layer(self, clr_md: color_mode = color_mode.line_type_based, alpha: float = 0.99,
                       is_draw_width: bool = False, is_clear_previous: bool = True,
                       track_ratio: int = 100, defects: list[DefectVisualization] = None):
        """Plot a single layer with optimized rendering for thousands of tracks."""
        alpha = 0.99 if alpha > 0.99 else alpha

        if self.ax is None:
            raise ValueError("plot ax is None")

        if is_clear_previous:
            self.clear_canvas()

        # Collect and organize tracks by type with ratio control
        track_data = self._collect_track_data(clr_md, track_ratio, defects)

        # Batch plotting mode (for performance)
        self._render_tracks_batch(track_data, is_draw_width, alpha)

        return self.ax

    def _collect_track_data(self, clr_md: color_mode = color_mode.line_type_based, track_ratio: float = 1.0,
                            defects: list[DefectVisualization] = None):
        """Collect and organize tracks by type and color with ratio control."""
        track_data = {
            'lines': [],
            'arcs': [],
            'circles': []
        }

        defect_track_uids = set()
        # Defect-based coloring removed. Gray mode is handled generically in get_track_color.

        tracks_to_process = self.get_tracks_to_process(track_ratio)
        self.calculate_extreme_values(tracks_to_process, clr_md)

        if clr_md == color_mode.layer_based:
            self.plt_layer_num += 1 if self.plt_layer_num < 7 else -7

        for track in tracks_to_process:
            if self.should_skip_track(track):
                continue

            color = self.get_track_color(track, clr_md)

            # Organize by motion type
            if track.motionType == MotionType.G1:
                track_data['lines'].append((track, color))
            elif track.motionType in [MotionType.G2, MotionType.G3]:
                track_data['arcs'].append((track, color))
            elif track.motionType == MotionType.pause:
                track_data['circles'].append((track, color))

        return track_data

    def _render_tracks_batch(self, track_data, is_draw_width, alpha) :
        """Render all tracks in batch mode for maximum performance."""
        # Prepare line segments for LineCollection
        line_segments = []
        line_colors = []

        for track, color in track_data['lines']:
            line_segments.append([(track.stPos[0], track.stPos[1]), (track.endPos[0], track.endPos[1])])
            line_colors.append(color)

        # Create arc patches in batch (with caching)
        arc_patches = []
        for track, color in track_data['arcs']:
            arc = self._get_cached_arc_patch(track, color)
            if arc:
                arc_patches.append(arc)

        # Create circle patches in batch
        circle_patches = []
        for track, color in track_data['circles']:
            circle = Circle((track.stPos[0], track.stPos[1]), 0.5, color=color, alpha=alpha, zorder=10)
            circle_patches.append(circle)

        # Add all patches at once for better performance
        if line_segments:
            line_collection = LineCollection(line_segments, colors=line_colors, linewidths=1, alpha=alpha)
            self.ax.add_collection(line_collection)

        if arc_patches:
            arc_collection = PatchCollection(arc_patches, match_original=True, alpha=alpha)
            self.ax.add_collection(arc_collection)

        if circle_patches:
            circle_collection = PatchCollection(circle_patches, match_original=True, alpha=alpha)
            self.ax.add_collection(circle_collection)

        # Handle width rendering if needed
        if is_draw_width:
            width_polygons = []

            # Line width polygons
            for track, color in track_data['lines']:
                poly = self.plt_line_width_polygon(track, color)
                if poly:
                    width_polygons.append(poly)

            # Arc width polygons (with caching)
            for track, color in track_data['arcs']:
                poly = self._get_cached_arc_width_polygon(track, color)
                if poly:
                    width_polygons.append(poly)

            if width_polygons:
                width_collection = PatchCollection(width_polygons, match_original=True, alpha=0.3, zorder=0)
                self.ax.add_collection(width_collection)

    def get_track_patch(self,track: TrackInfo, is_draw_width, color, alpha):
        """create patch of track according to its MotionType(G1/G2/G3), color, whther draw width"""
        if is_draw_width:
            if track.motionType == MotionType.G1:
                return self.plt_line_width_polygon(track, color, alpha)
            elif track.motionType in [MotionType.G2, MotionType.G3]:
                return self._get_cached_arc_width_polygon(track, color, alpha)
        else:
            if track.motionType == MotionType.G1:
                return Line2D((track.stPos[0], track.endPos[0]), (track.stPos[1], track.endPos[1]), color=color, alpha=alpha)
            elif track.motionType in [MotionType.G2, MotionType.G3]:
                return self._get_cached_arc_patch(track, color, alpha)
            elif track.motionType == MotionType.pause:
                return Circle((track.stPos[0], track.stPos[1]), 0.5, color=color, alpha=alpha, zorder=10)
        return None

    def plt_arc_patch(self, track: TrackInfo, color, alpha=1.0):
        """Create arc patch from track data"""
        arc_paras = track.get_arc_paras()
        center = arc_paras['center']
        start_angle = np.degrees(arc_paras['start_angle_rad'])
        end_angle = np.degrees(arc_paras['end_angle_rad'])
        radius = arc_paras['radius']

        return Arc(center, 2 * radius, 2 * radius,
                  theta1=min(start_angle, end_angle),
                  theta2=max(start_angle, end_angle), edgecolor=color,
                  fill=False, facecolor='none', alpha=alpha)

    def plt_line_width_polygon(self, track: TrackInfo, color, alpha=0.3):
        """Create a polygon representing a line with width."""
        from gcode_defect.utils.poly import get_line_width_polygon_points
        pts = get_line_width_polygon_points(track.stPos, track.endPos, track.width)
        if pts:
            return MplPolygon(pts, closed=True, color=color, alpha=alpha)
        else:
            return None

    def plt_arc_width_polygon(self, track: TrackInfo, color, alpha=0.3):
        """Create a polygon representing an arc with width."""
        from gcode_defect.utils.poly import get_arc_width_polygon_points
        outer_points, inner_points = get_arc_width_polygon_points(track.stPos, track.endPos, track.cicPos, track.motionType == MotionType.G2, track.width, 
                                                                chord_error_mm=track.chord_error_mm, max_discrete_num=track.max_discrete_num)
        polygon_points = outer_points + inner_points
        if polygon_points:
            return MplPolygon(polygon_points, closed=True, color=color, alpha=alpha)
        else:
            return None

    def _arc_cache_key(self, track: TrackInfo, color, alpha=1.0):
        uid = getattr(track, 'uid', None)
        return (uid if uid is not None else id(track), color, round(alpha, 3))

    def _get_cached_arc_patch(self, track: TrackInfo, color, alpha=1.0):
        key = self._arc_cache_key(track, color, alpha)
        cached = self._arc_patch_cache.get(key)
        if cached is not None:
            return cached
        # Build from precomputed arc parameters
        arc_paras = track.get_arc_paras()
        center = arc_paras['center']
        start_angle = np.degrees(arc_paras['start_angle_rad'])
        end_angle = np.degrees(arc_paras['end_angle_rad'])
        radius = arc_paras['radius']
        arc = Arc(center, 2 * radius, 2 * radius,
                  theta1=min(start_angle, end_angle),
                  theta2=max(start_angle, end_angle), edgecolor=color,
                  fill=False, facecolor='none', alpha=alpha)
        self._arc_patch_cache[key] = arc
        return arc

    def _get_cached_arc_width_polygon(self, track: TrackInfo, color, alpha=0.3):
        key = self._arc_cache_key(track, color, alpha)
        cached = self._arc_width_polygon_cache.get(key)
        if cached is not None:
            return cached
        # Uses precomputed sampling stored during import (store_discrete_data)
        poly = self.plt_arc_width_polygon(track, color, alpha)
        if poly:
            self._arc_width_polygon_cache[key] = poly
        return poly


    def _draw_polygon(self, geom: any, **style):
        """
        Draw shapely geometry objects (Polygon, MultiPolygon, GeometryCollection).
        """
        if not geom or not hasattr(geom, 'is_valid') or not geom.is_valid:
            return

        # Use a common style dictionary, allowing overrides
        default_style = {'facecolor': 'gray', 'edgecolor': 'gray', 'alpha': 0.3, 'zorder': 10}
        final_style = {**default_style, **style}

        if isinstance(geom, Polygon):
            coords = list(geom.exterior.coords)
            patch = MplPolygon(coords, closed=True, **final_style)
            self.ax.add_patch(patch)

        elif isinstance(geom, (MultiPolygon, GeometryCollection)):
            for sub_geom in geom.geoms:
                self._draw_polygon(sub_geom, **style)

    def _draw_point(self, geom: tuple, **style):
        """Draw a single point using ax.scatter."""
        default_style = {'color': 'red', 's': 20, 'zorder': 10, 'marker': 'o'}
        final_style = {**default_style, **style}
        self.ax.scatter(geom[0], geom[1], **final_style)

    def _add_text(self, geom: tuple, text: str, **style):
        """Add a text annotation to the plot."""
        default_style = {'fontsize': 8, 'ha': 'center', 'zorder': 15}
        final_style = {**default_style, **style}
        self.ax.text(geom[0], geom[1], text, **final_style)

    def plot_defects(self, vis_data: dict[int, list[DefectVisualization]]):
        """Universal method to plot all types of defects from a list of DefectVisualization objects."""
        if not vis_data or self.ax is None:
            return

        for vis in vis_data:
            for item in vis.drawables:
                if item.type == 'polygon_2d':
                    self._draw_polygon(item.geometry, **item.style)
                elif item.type == 'track':
                    # Create the patch for the track
                    patch = self.get_track_patch(item.geometry, is_draw_width=False, color=item.style.get('color', 'red'), alpha=item.style.get('alpha', 1.0))
                    # Add the created patch to the axes so it gets drawn
                    if patch:
                        if isinstance(patch, Line2D):
                            self.ax.add_line(patch)
                        else:
                            self.ax.add_patch(patch)
                elif item.type == 'point':
                    self._draw_point(item.geometry, **item.style)
                elif item.type == 'text_annotation':
                    self._add_text(item.geometry, item.text, **item.style)
