from __future__ import annotations
from typing import Any
import numpy as np

class ColorMapper:
    """
    A helper class to map a range of numerical values to a color gradient.
    It handles normalization, color lookup, and legend generation.
    """
    def __init__(self, colormap: list[str], min_val: float | None = None, max_val: float | None = None):
        """
        Initializes the ColorMapper.

        Args:
            colormap: A list of hex color strings representing the gradient.
            min_val: Optional. A fixed minimum value for the scale.
            max_val: Optional. A fixed maximum value for the scale.
        """
        if not colormap:
            raise ValueError("Colormap cannot be empty.")
        self.colormap = colormap
        self.min_val = min_val
        self.max_val = max_val
        self._is_fitted = min_val is not None and max_val is not None

    def fit(self, values: list[float]):
        """
        Automatically determines the min and max values from a list of data.
        This is ignored if min_val and max_val were set during initialization.

        Args:
            values: A list of numerical values to fit the scale to.
        """
        if self._is_fitted:
            return  # Use pre-defined min/max

        if not values:
            self.min_val = 0.0
            self.max_val = 1.0
        else:
            self.min_val = min(values)
            self.max_val = max(values)
        
        self._is_fitted = True

    def get_color(self, value: float) -> str:
        """
        Maps a single numerical value to a color from the gradient.

        Args:
            value: The numerical value to map.

        Returns:
            A hex color string.
        """
        if not self._is_fitted:
            raise RuntimeError("ColorMapper must be fitted with .fit(values) before get_color can be used.")

        # Normalize the value within the min/max range
        val_range = self.max_val - self.min_val
        if val_range < 1e-9:
            normalized_value = 0.5  # Avoid division by zero, return middle color
        else:
            normalized_value = (value - self.min_val) / val_range

        # Clamp the value between 0 and 1
        normalized_value = np.clip(normalized_value, 0.0, 1.0)

        # Calculate the index in the colormap
        color_index = int(normalized_value * (len(self.colormap) - 1))
        return self.colormap[color_index]

    def get_legend_info(self, title: str) -> dict[str, Any]:
        """
        Generates a dictionary containing all necessary information for a UI legend.

        Args:
            title: The title to be displayed for the legend (e.g., "Overlap Ratio").

        Returns:
            A dictionary with title, min/max values, and the colormap.
        """
        if not self._is_fitted:
            raise RuntimeError("ColorMapper must be fitted before get_legend_info can be used.")
        
        return {
            'title': title,
            'max_value': self.max_val,
            'min_value': self.min_val,
            'colormap': self.colormap
        }