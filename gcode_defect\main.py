"""Main entry point for launching the G-Code Defect Analyzer application."""

import sys

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

from gcode_defect.UI.Qt_Mainwindow import Qt_Mainwindow


def main():
    """Initializes and runs the Qt application."""
    # Enable high DPI support for better display quality
    # Note: AA_EnableHighDpiScaling and AA_UseHighDpiPixmaps are deprecated in Qt6
    # and enabled by default, so we only need to set the rounding policy
    QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)

    app = QApplication(sys.argv)
    window = Qt_Mainwindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()