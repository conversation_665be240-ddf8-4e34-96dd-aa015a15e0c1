import math
from typing import Any
from gcode_defect.Defect.Base import DefectAnalyzerBase, DefectVisualization, Drawable
import numpy as np
from gcode_defect.GcodeParse.ModelInfo import LineType, MotionType, TrackInfo
from gcode_defect.utils.line_segment import LineSegment, discretize_tracks
from gcode_defect.utils.spatial_indexing import build_angle_binned_rtree
from gcode_defect.utils.visualization import ColorMapper

TRACK_CHECK_TYPE = [LineType.Outer_wall, 
                    LineType.Inner_wall, 
                    LineType.Overhang_wall
                    ]

class LineShiftAnalyser(DefectAnalyzerBase):
    
    @property
    def defect_name(self) -> str:
        return "line_shift"

    @property
    def analysis_params(self) -> list[dict[str, Any]]:
        return [
            {'name': '修正系数', 'id': 'k_factor', 'type': 'float', 'default': 0.0},
            {'name': '平行角度 (deg)', 'id': 'parallel_angle_threshold', 'type': 'float', 'default': 10.0},
            {'name': '线段采样长度 (mm)', 'id': 'line_samp_length', 'type': 'float', 'default': 1}
        ]

    def analysis(self, model, precomputed: dict[str, Any], **kwargs):
        params = {p['id']: p['default'] for p in self.analysis_params}
        params.update(kwargs)

        defect_by_layers = {}
    
        for layer_id, layer in enumerate(model.layers):
            if layer_id % 10 == 0:
                print(f"Layer {layer_id} line shift defect analysis started")

            tracks_to_check = [track for track in layer.tracks if track.lineType in TRACK_CHECK_TYPE]
            expended_track_info = self.calculate_updated_widths(tracks_to_check,
                                                                layer_height = layer.height,
                                                                k_factor = params['k_factor'],
                                                                parallel_angle_threshold = params['parallel_angle_threshold'],
                                                                line_samp_length = params['line_samp_length'])

            if not expended_track_info:
                continue
            
            defect_by_layers[layer_id] = []
            for segment_info in expended_track_info:
                original_track = segment_info['original_track']
                if original_track.lineType == LineType.Outer_wall:
                    defect_by_layers[layer_id].append(segment_info)
        return defect_by_layers

    @staticmethod
    def defect_visualization(defect_by_layers):
        if not defect_by_layers:
            return None

        # A 10-step blue-to-yellow-to-red color scale for visualizing severity
        COLOR_GRADIENT = [
            '#313695',
            '#4575b4',
            '#74add1',
            '#abd9e9',
            '#e0f3f8',
            '#fee090',
            '#fdae61',
            '#f46d43',
            '#d73027',
            '#a50026'
        ]

        # --- 1. Use ColorMapper to handle normalization and legend ---
        all_widths = [d['expand_width'] for defects in defect_by_layers.values() for d in defects]
        if not all_widths:
            return None

        # Use fixed min/max as before, but now managed by the mapper
        mapper = ColorMapper(COLOR_GRADIENT, min_val=0.005, max_val=0.04)

        # --- 2. Create drawables for each defect with a color based on severity ---
        vis_by_layer: dict[int, DefectVisualization] = {}
        for layer_id, defects in defect_by_layers.items():
            if layer_id not in vis_by_layer:
                vis_by_layer[layer_id] = DefectVisualization(metadata={'layer_id': layer_id})
            for defect in defects:
                shifted_segment = defect['shifted_line_segment']
                original_track = defect['original_track']
                z_height = original_track.stPos[2]
                shifted_segment_as_track = TrackInfo(
                    st_pos=(*shifted_segment.start, z_height),
                    end_pos=(*shifted_segment.end, z_height),
                    motionType=MotionType.G1,
                    width=shifted_segment.updated_width,
                    height=original_track.height,
                    layer_id=original_track.layer_id
                )

                # --- Get color from the mapper ---
                defect_color = mapper.get_color(defect['expand_width'])
                style = {'color': defect_color, 'linewidth': 2}

                vis_by_layer[layer_id].drawables.append(
                    Drawable(type='track', geometry=shifted_segment_as_track, style=style)
                )

        visualizations = list(vis_by_layer.values())
        if visualizations:
            # --- 3. Get legend info from the mapper ---
            legend_info = mapper.get_legend_info('Expand Width (mm)')
            for vis in visualizations:
                vis.metadata['legend_info'] = legend_info

        return visualizations

    @staticmethod
    def _is_parallel(line_i: LineSegment, line_j: LineSegment, angle_threshold) -> bool:
        """Checks if two lines are parallel within the defined angle threshold."""
        if line_i.length == 0 or line_j.length == 0:
            return False
        dot_product = abs(np.dot(line_i.normalized_vector, line_j.normalized_vector))
        return dot_product > angle_threshold

    @staticmethod
    def _calculate_overlap_area(line_i: LineSegment, line_j: LineSegment, width_i: float, width_j: float) -> float:
        """
        Calculates the overlapping area of two nearly parallel line segments. The area is approximated as a parallelogram (length * width of overlap)
        Returns 0 if they do not overlap.
        """
        # --- 1. Check for lateral (sideways) overlap ---
        vec_start_to_start = line_i.start - line_j.start
        distance_lateral = abs(np.cross(line_j.normalized_vector, vec_start_to_start))
        sum_half_widths = (width_i / 2) + (width_j / 2)

        if distance_lateral >= sum_half_widths:
            return 0, 0 , None

        # --- 2. Calculate longitudinal (lengthwise) overlap ---
        proj_i_start = np.dot(line_i.start - line_j.start, line_j.normalized_vector)
        proj_i_end = np.dot(line_i.end - line_j.start, line_j.normalized_vector)
        interval_i = sorted([proj_i_start, proj_i_end])
        interval_j = [0, line_j.length]

        overlap_start = max(interval_i[0], interval_j[0])
        overlap_end = min(interval_i[1], interval_j[1])

        overlap_length = max(0, overlap_end - overlap_start)

        if overlap_length < 0.05 * line_i.length:
            return 0, 0 , None

        # --- 3. Calculate the area ---
        overlap_width = sum_half_widths - distance_lateral

        # dir is prependicular vector of line_i in xy plane and points in direction away form line_j
        dir = np.array([-line_i.normalized_vector[1], line_i.normalized_vector[0]])
        if np.dot(vec_start_to_start, dir) < 0:
            dir = -dir
        return overlap_length, overlap_width, dir

    def calculate_updated_widths(self, tracks: list[TrackInfo], **kwargs) -> list[dict[str, Any]]:
        """
        Processes a series of lines, updating their widths based on parallel overlaps.
        This version is optimized using R-trees with angle binning to achieve O(n log n) complexity.

        Args:
            tracks: A list of TrackInfo objects.
            **kwargs: dictionary of analysis parameters.

        Returns:
            A list of dictionaries, each containing information about a shifted line segment.
        """
        ANGLE_BIN_SIZE = 10
        angle_threshold = math.cos(math.radians(kwargs['parallel_angle_threshold']))

        if len(tracks) < 2:
            return []

        # --- 1. Discretize all tracks into small LineSegment objects ---
        lines: list[LineSegment] = discretize_tracks(tracks, kwargs['line_samp_length'])

        # --- 2. Build spatial indices (R-trees) binned by angle ---
        angle_bin_size = 10  # degrees
        angle_binned_rtrees = build_angle_binned_rtree(lines, angle_bin_size)
        # --- 3. Query R-trees and process overlaps ---
        expanded_track_info = []
        for i in range(len(lines)):
            current_line = lines[i]
            if current_line.length <= 1e-9:
                continue

            # Determine which angle bins to query
            angle = np.degrees(np.arctan2(current_line.normalized_vector[1], current_line.normalized_vector[0]))
            normalized_angle = angle % 180
            bin_key = int(normalized_angle / angle_bin_size) * angle_bin_size

            bins_to_check = {bin_key}
            # Also check adjacent bins to handle lines near bin boundaries
            bins_to_check.add((bin_key - ANGLE_BIN_SIZE + 180) % 180)
            bins_to_check.add((bin_key + ANGLE_BIN_SIZE) % 180)

            # Get bounding box for the query
            # This bbox is based on the line's state before being processed in this iteration.
            bbox = current_line.get_axis_aligned_bounding_box()
            candidate_indices = set()
            for b in bins_to_check:
                if b in angle_binned_rtrees:
                    candidate_indices.update(angle_binned_rtrees[b].intersection(bbox))

            # Process candidate lines
            overlap_length_list = []
            overlap_width_list = []
            dir_list = []

            for j in candidate_indices:
                # Avoid self-comparison and processing future lines (which would be double-counting)
                if j >= i:
                    continue

                prev_line = lines[j]

                # R-tree gives broad phase collision, now do narrow phase
                if self._is_parallel(current_line, prev_line, angle_threshold):
                    ol, ow, d = self._calculate_overlap_area(current_line, prev_line,
                                                             current_line.initial_width, prev_line.updated_width)
                    if ol:
                        overlap_length_list.append(ol)
                        overlap_width_list.append(ow)
                        dir_list.append(d)

            if not overlap_length_list:
                continue

            # To maintain correctness, the R-tree must be updated with the new geometry.
            # The line's angle does not change, so it stays in the same R-tree bin.
            rtree_to_update = angle_binned_rtrees.get(bin_key)
            if rtree_to_update:
                # Delete the old entry. This assumes `build_angle_binned_rtree` uses the
                # list index `i` as the item ID. The `bbox` must match the one used for insertion.
                rtree_to_update.delete(i, bbox)

            dir = np.mean(dir_list, axis=0)
            overflow_vol = self._calculate_overflow_volumn(overlap_width_list, overlap_length_list, kwargs['layer_height'], kwargs['k_factor'])
            expand_width = overflow_vol/current_line.length/ kwargs['layer_height']

            current_line.updated_width = current_line.initial_width + expand_width
            current_line = self._shift_line(current_line, expand_width/2, dir)

            # Insert the new entry with the updated bounding box.
            if rtree_to_update:
                new_bbox = current_line.get_axis_aligned_bounding_box()
                rtree_to_update.insert(i, new_bbox)

            if expand_width > 0.005 * current_line.initial_width:
                expanded_track_info.append({
                    'original_track': current_line.origin_track,
                    'expand_width': expand_width,
                    'shifted_line_segment': current_line
                })

        return expanded_track_info

    def calculate_updated_widths_for_segments(self, lines: list[LineSegment], **kwargs) -> list[LineSegment]:
        """
        Processes an existing list of LineSegment objects (possibly pre-expanded),
        updating their widths based on parallel overlaps. Returns the subset of
        segments that received a significant expansion, with .expand_width set and
        geometry shifted accordingly.

        Args:
            lines: Pre-discretized LineSegment objects. Each should have initial_width and updated_width set;
                   updated_width may include prior "glue" expansions.
            **kwargs: parallel_angle_threshold (deg), layer_height, k_factor

        Returns:
            list[LineSegment]: segments that expanded beyond a small threshold, annotated with expand_width
        """
        angle_threshold = math.cos(math.radians(kwargs['parallel_angle_threshold']))
        if not lines or len(lines) < 2:
            return []

        expanded_segments: list[LineSegment] = []

        # Build spatial indices (R-trees) binned by angle
        angle_bin_size = 10  # degrees
        angle_binned_rtrees = build_angle_binned_rtree(lines, angle_bin_size)
        # Query R-trees and process overlaps
        for i in range(len(lines)):
            current_line = lines[i]
            if getattr(current_line, 'length', 0.0) is None or current_line.length <= 1e-9:
                continue

            angle = np.degrees(np.arctan2(current_line.normalized_vector[1], current_line.normalized_vector[0]))
            normalized_angle = angle % 180
            bin_key = int(normalized_angle / angle_bin_size) * angle_bin_size

            bins_to_check = {bin_key}
            bins_to_check.add((bin_key - angle_bin_size + 180) % 180)
            bins_to_check.add((bin_key + angle_bin_size) % 180)

            # This bbox is based on the line's state before being processed in this iteration.
            bbox = current_line.get_axis_aligned_bounding_box()
            candidate_indices = set()
            for b in bins_to_check:
                if b in angle_binned_rtrees:
                    candidate_indices.update(angle_binned_rtrees[b].intersection(bbox))

            overlap_length_list = []
            overlap_width_list = []
            dir_list = []

            for j in candidate_indices:
                if j >= i:
                    continue
                prev_line = lines[j]
                if self._is_parallel(current_line, prev_line, angle_threshold):
                    ol, ow, d = self._calculate_overlap_area(current_line, prev_line,
                                                             current_line.updated_width, prev_line.updated_width)
                    if ol:
                        overlap_length_list.append(ol)
                        overlap_width_list.append(ow)
                        dir_list.append(d)

            if not overlap_length_list:
                continue

            # To maintain correctness, the R-tree must be updated with the new geometry.
            # The line's angle does not change, so it stays in the same R-tree bin.
            rtree_to_update = angle_binned_rtrees.get(bin_key)
            if rtree_to_update:
                # Delete the old entry. This assumes `build_angle_binned_rtree` uses the
                # list index `i` as the item ID. The `bbox` must match the one used for insertion.
                rtree_to_update.delete(i, bbox)

            dir = np.mean(dir_list, axis=0)
            overflow_vol = self._calculate_overflow_volumn(overlap_width_list, overlap_length_list, kwargs['layer_height'], kwargs['k_factor'])
            expand_width = overflow_vol / current_line.length / kwargs['layer_height']

            current_line.updated_width = current_line.updated_width + expand_width
            current_line = self._shift_line(current_line, expand_width / 2, dir)

            if rtree_to_update:
                new_bbox = current_line.get_axis_aligned_bounding_box()
                rtree_to_update.insert(i, new_bbox)

            if expand_width > 0.005 * current_line.initial_width:
                # annotate and collect
                setattr(current_line, 'expand_width', expand_width)
                expanded_segments.append(current_line)

        return expanded_segments

    @staticmethod
    def _calculate_overflow_volumn(overlap_width_list, overlap_length_list, layer_height, k_factor):
        # total_overlap_area = sum(np.array(overlap_length_list) * np.array(overlap_width_list))
        # total_overlap_length = sum(overlap_length_list)
        # ovp_width = total_overlap_area / total_overlap_length if total_overlap_length > 0 else 0

        r = layer_height / 2
        overflow_area = []
        for ovp_width in overlap_width_list:
            if ovp_width < layer_height:
                # calculate the overlap area of two circle with a diameter of layer_height and a overlap depth of avg_ovp_width
                d = r - ovp_width / 2
                theta = math.acos(d/r)
                overlap_area = r**2 * (theta * 2 - math.sin(2 * theta))
                empty_area = (1 - math.pi / 4) * layer_height**2 - layer_height * ovp_width + overlap_area
                oa = overlap_area - empty_area * (0.5 + k_factor * 0.5)
                if oa > 0:
                    overflow_area.append(oa)
                else:
                    overflow_area.append(0)
            else:
                overlap_area = math.pi * r**2 + (ovp_width - layer_height) * layer_height
                overflow_area.append(overlap_area)

        overflow_volumn = sum (np.array(overflow_area) * np.array(overlap_length_list))

        return overflow_volumn

    @staticmethod
    def _shift_line(current_line, offset, dir):
        current_line.start += dir * offset
        current_line.end += dir * offset
        return current_line