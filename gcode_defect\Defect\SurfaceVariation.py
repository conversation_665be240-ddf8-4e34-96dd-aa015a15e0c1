import numpy as np
from collections import defaultdict
from typing import Any

from scipy.spatial import KDTree

from gcode_defect.Defect.Base import DefectAnalyzerBase, DefectVisualization, Drawable
from gcode_defect.GcodeParse.ModelInfo import ModelInfo
from gcode_defect.Defect.LineShift import LineSegment
from gcode_defect.utils.visualization import ColorMapper

class SurfaceVariationAnalyzer(DefectAnalyzerBase):
    @property
    def defect_name(self) -> str:
        return "surface_variation"

    @property
    def analysis_params(self) -> list[dict[str, Any]]:
        return [
            {'name': 'Z-变化阈值 (mm)', 'id': 'z_threshold_mm', 'type': 'float', 'default': 0.005},
            {'name': 'XY-变化阈值 (mm)', 'id': 'xy_threshold_mm', 'type': 'float', 'default': 0.005},
            {'name': 'XY-窗口大小 (mm)', 'id': 'xy_window_size_mm', 'type': 'float', 'default': 3.0},
        ]

    def analysis(self, model: ModelInfo, precomputed: dict[str, Any], **kwargs):
        params = {p['id']: p['default'] for p in self.analysis_params}
        params.update(kwargs)

        # The orchestrator can pass precomputed segments, e.g., from UnifiedSurfaceAnalyzer.
        # If not, we generate them from the line_shift dependency.
        if 'precomputed_segments_by_layer' in kwargs:
            segments_by_layer = kwargs['precomputed_segments_by_layer']
        else:
            # --- Step 1: Get pre-computed line shift results from the orchestrator ---
            line_shift_results = precomputed.get('line_shift', {})

            # --- Step 2: Structure the data for efficient access ---
            segments_by_layer = defaultdict(list)
            for layer_id, defects in line_shift_results.items():
                for defect in defects:
                    segment: LineSegment = defect['shifted_line_segment']
                    segment.expand_width = defect['expand_width']
                    segment.z_height = defect['original_track'].stPos[2]
                    setattr(segment, 'origin_track', defect.get('original_track', None))
                    segments_by_layer[layer_id].append(segment)

        # --- Step 3: Z-Axis Analysis (Inter-Layer Variation) ---
        z_defects = self._analyze_z_variation(segments_by_layer, params['z_threshold_mm'])

        # --- Step 4: XY-Axis Analysis (Intra-Layer Variation) ---
        xy_defects = self._analyze_xy_variation(segments_by_layer, params['xy_threshold_mm'], params['xy_window_size_mm'])

        return {'z_defects': z_defects, 'xy_defects': xy_defects}

    def _analyze_z_variation(self, segments_by_layer: dict[int, list[LineSegment]], z_threshold: float) -> list[dict]:
        """Finds sudden changes in expand_width between adjacent layers."""
        z_defects = []
        sorted_layers = sorted(segments_by_layer.keys())

        for i in range(1, len(sorted_layers)):
            current_layer_id = sorted_layers[i]
            below_layer_id = sorted_layers[i-1]

            current_segments = segments_by_layer[current_layer_id]
            below_segments = segments_by_layer[below_layer_id]

            if not below_segments:
                continue

            # Build a KD-Tree for fast nearest-neighbor search on the layer below
            below_points = [seg.start for seg in below_segments]
            below_kdtree = KDTree(below_points)

            for segment in current_segments:
                # Find the closest segment on the layer below
                dist, idx = below_kdtree.query(segment.start)
                
                # Check if the found segment is truly "below" the current one
                if dist < segment.initial_width:
                    below_segment = below_segments[idx]
                    delta = segment.expand_width - below_segment.expand_width

                    if abs(delta) > z_threshold:
                        z_defects.append({
                            'layer_id': current_layer_id,
                            'point': (segment.start[0], segment.start[1], segment.z_height),
                            'delta': delta
                        })
        return z_defects

    def _analyze_xy_variation(self, segments_by_layer: dict[int, list[LineSegment]], xy_threshold: float, window_size: float) -> list[dict]:
        """Finds local variations in expand_width along a single layer's contours."""
        xy_defects = []
        
        for layer_id, segments in segments_by_layer.items():
            # Group segments by their original track to approximate contours
            segments_by_track = defaultdict(list)
            for seg in segments:
                track_key = getattr(seg, 'origin_track', None)
                if track_key is None:
                    # fallback: group by approximate start point to maintain continuity
                    track_key = ('fallback', round(seg.start[0], 3), round(seg.start[1], 3))
                segments_by_track[track_key].append(seg)

            for track_key, contour_segments in segments_by_track.items():
                if len(contour_segments) < 3:
                    continue

                widths = np.array([s.expand_width for s in contour_segments])
                lengths = np.array([s.length for s in contour_segments])
                cumulative_lengths = np.insert(np.cumsum(lengths), 0, 0)

                for i, segment in enumerate(contour_segments):
                    # Define the window for local average calculation
                    current_pos = cumulative_lengths[i]
                    window_start = current_pos - window_size / 2.0
                    window_end = current_pos + window_size / 2.0

                    # Find indices of segments within the window
                    indices = np.where((cumulative_lengths[:-1] >= window_start) & (cumulative_lengths[:-1] < window_end))[0]
                    
                    if len(indices) > 1:
                        # Calculate weighted average, excluding the current point
                        local_indices = [idx for idx in indices if idx != i]
                        if not local_indices: continue
                        
                        local_widths = widths[local_indices]
                        local_lengths = lengths[local_indices]
                        local_avg_width = np.sum(local_widths * local_lengths) / np.sum(local_lengths)
                        
                        delta = segment.expand_width - local_avg_width
                        if abs(delta) > xy_threshold:
                            xy_defects.append({
                                'layer_id': layer_id,
                                'point': (segment.start[0], segment.start[1], segment.z_height),
                                'delta': delta
                            })
        return xy_defects

    @staticmethod
    def defect_visualization(analysis_result):
        z_defects = analysis_result.get('z_defects', [])
        xy_defects = analysis_result.get('xy_defects', [])
        all_defects = z_defects + xy_defects

        if not all_defects:
            return None

        # A diverging color map: Blue -> White -> Red
        DIVERGING_COLORMAP = [
            '#053061', '#2166ac', '#4393c3', '#92c5de', '#d1e5f0',
            '#f7f7f7',
            '#fddbc7', '#f4a582', '#d6604d', '#b2182b', '#67001f'
        ]

        # --- 1. Normalize the color scale based on the max absolute delta ---
        all_deltas = [d['delta'] for d in all_defects]
        if not all_deltas: return None

        max_abs_delta = max(abs(d) for d in all_deltas)
        if max_abs_delta < 1e-9:
            return None

        mapper = ColorMapper(DIVERGING_COLORMAP, min_val=-max_abs_delta, max_val=max_abs_delta)

        # --- 2. Create drawables for each defect point ---
        vis_by_layer: dict[int, DefectVisualization] = defaultdict(lambda: DefectVisualization())
        for defect in all_defects:
            layer_id = defect['layer_id']
            vis_by_layer[layer_id].metadata['layer_id'] = layer_id

            defect_color = mapper.get_color(defect['delta'])
            style = {'color': defect_color, 's': 15, 'marker': 'o', 'zorder': 20}
            
            vis_by_layer[layer_id].drawables.append(
                Drawable(type='point', geometry=defect['point'], style=style)
            )

        visualizations = list(vis_by_layer.values())
        if visualizations:
            legend_info = mapper.get_legend_info('Surface Variation (mm)')
            for vis in visualizations:
                vis.metadata['legend_info'] = legend_info
        
        return visualizations
