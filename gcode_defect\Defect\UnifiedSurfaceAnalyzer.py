from typing import Any
from collections import defaultdict
import numpy as np
from scipy.spatial import KDTree

# Import the refactored modules.
from gcode_defect.Defect.LineShift import LineShiftAnalyser, LineSegment
from gcode_defect.Defect.Overlap import OverlapAnalyzer
from gcode_defect.Defect.SurfaceVariation import SurfaceVariationAnalyzer
from gcode_defect.Defect.Base import DefectAnalyzerBase, DefectVisualization, Drawable
from gcode_defect.GcodeParse.ModelInfo import ModelInfo
from gcode_defect.utils.visualization import ColorMapper


class UnifiedSurfaceAnalyzer(DefectAnalyzerBase):
    """
    Orchestrates the entire surface defect analysis process by coordinating
    the Overlap, LineShift, and SurfaceVariation analyzers.
    """

    @property
    def defect_name(self) -> str:
        return "unified_surface"

    @property
    def analysis_params(self) -> list[dict[str, Any]]:
        return self._analysis_params

    def __init__(self, config: dict[str, Any] | None = None):
        self.config = config or {}
        self.overlap_analyzer = OverlapAnalyzer()
        self.lineshift_analyzer = LineShiftAnalyser()
        self.surface_variation_analyzer = SurfaceVariationAnalyzer()

        # Combine params from all analyzers, avoiding duplicates.
        all_params = []
        seen_ids = set()

        # Start with its own specific params for the "glue logic"
        own_params = [
            {'name': '角点影响半径 (mm)', 'id': 'corner_influence_radius', 'type': 'float', 'default': 1.0},
            {'name': '角点影响比例', 'id': 'corner_influence_scale', 'type': 'float', 'default': 0.25},
            {'name': '角点最大附加线宽 (mm)', 'id': 'corner_max_additional_w', 'type': 'float', 'default': 0.08}
        ]

        # Aggregate all parameters
        param_sources = own_params + self.lineshift_analyzer.analysis_params + self.surface_variation_analyzer.analysis_params
        for p in param_sources:
            if p['id'] not in seen_ids:
                all_params.append(p)
                seen_ids.add(p['id'])

        self._analysis_params = all_params

    def analysis(self, model: ModelInfo, precomputed: dict[str, Any], **kwargs) -> dict[str, Any]:
        """
        Manages the entire defect analysis workflow from start to finish.
        This analyzer acts as a complex workflow:
        1. It uses pre-computed Overlap results.
        2. It uses pre-computed LineShift results to get initial segment expansions.
        3. It applies "glue logic" to further modify segment widths based on corner overlaps.
        4. It runs the final surface variation analysis on these modified segments.

        Args:
            model: The full ModelInfo object containing layers and tracks.
            precomputed: dictionary from the orchestrator containing 'overlap' and 'line_shift' results.
            **kwargs: User-defined parameters.

        Returns:
            A dictionary with keys 'z_defects' and 'xy_defects' (lists of defect dicts).
        """
        # Merge config with per-call overrides
        params = {**self.config, **kwargs}
        
        # --- Step 1: Get pre-computed results from dependencies ---
        overlap_results = precomputed.get('overlap', [])
        line_shift_results = precomputed.get('line_shift', {})

        # --- Step 2: Re-create the segments_by_layer structure from line_shift_results ---
        # This structure will be the basis for our modifications.
        segments_by_layer = defaultdict(list)
        for layer_id, defects in line_shift_results.items():
            for defect in defects:
                segment: LineSegment = defect['shifted_line_segment']
                # This is the expansion from parallel lines (the LineShift result)
                segment.expand_width = defect['expand_width']
                segment.z_height = defect['original_track'].stPos[2]
                setattr(segment, 'origin_track', defect.get('original_track', None))
                segments_by_layer[layer_id].append(segment)

        # --- Step 3: Apply "glue logic" to modify segment widths based on corner overlaps ---
        for layer_id, segs in segments_by_layer.items():
            if not segs: continue

            # We need a fast way to find segments by position. Build a KDTree on segment midpoints.
            pts = [0.5 * (s.start + s.end) for s in segs]
            if not pts: continue
            tree = KDTree(pts)

            # Find overlap defects for the current layer
            layer_defects = [d for d in overlap_results if d.get('layer_id') == layer_id]
            for d in layer_defects:
                center_xy = np.array(d['detect_point'][:2])
                r = params['corner_influence_radius']
                idxs = tree.query_ball_point(center_xy, r=r)
                if not idxs: continue

                # Map overlap volume to an additional width, then apply it to nearby segments
                ov = max(0.0, d.get('overlap_volumn', 0.0))
                layer_height = getattr(model.layers[layer_id], 'height', 0.2)
                denom = max(1e-6, r * layer_height)  # Effective area over which volume is spread
                scale = params['corner_influence_scale']
                max_add = params['corner_max_additional_w']
                additional_w = min(max_add, scale * ov / denom)

                for idx in idxs:
                    s = segs[idx]
                    # Add the corner-based expansion to the existing parallel-based expansion
                    s.expand_width += additional_w

        # --- Step 4: Run the final Surface Variation analysis on the modified segments ---
        # We call the SurfaceVariationAnalyzer's analysis method, but provide it with our
        # custom-processed segments. This avoids duplicating the Z and XY variation logic.
        sv_params = {k: v for k, v in params.items() if k in [p['id'] for p in self.surface_variation_analyzer.analysis_params]}
        return self.surface_variation_analyzer.analysis(
            model,
            precomputed={},  # Pass empty precomputed as we are providing segments directly
            precomputed_segments_by_layer=segments_by_layer,
            **sv_params
        )


    @staticmethod
    def defect_visualization(analysis_result):
        z_defects = analysis_result.get('z_defects', []) if analysis_result else []
        xy_defects = analysis_result.get('xy_defects', []) if analysis_result else []
        all_defects = z_defects + xy_defects
        if not all_defects:
            return None

        # Use the same diverging colormap and legend format as SurfaceVariation
        DIVERGING_COLORMAP = [
            '#053061', '#2166ac', '#4393c3', '#92c5de', '#d1e5f0',
            '#f7f7f7',
            '#fddbc7', '#f4a582', '#d6604d', '#b2182b', '#67001f'
        ]

        # --- 1. Use ColorMapper to handle normalization and legend ---
        all_deltas = [d.get('delta', 0.0) for d in all_defects]
        if not all_deltas:
            return None

        max_abs_delta = max(abs(d) for d in all_deltas)
        if max_abs_delta < 1e-9:
            return None

        mapper = ColorMapper(DIVERGING_COLORMAP, min_val=-max_abs_delta, max_val=max_abs_delta)

        vis_by_layer = defaultdict(lambda: DefectVisualization())
        for defect in all_defects:
            layer_id = defect['layer_id']
            vis_by_layer[layer_id].metadata['layer_id'] = layer_id
            color = mapper.get_color(defect.get('delta', 0.0))
            style = {'color': color, 's': 15, 'marker': 'o', 'zorder': 20}
            vis_by_layer[layer_id].drawables.append(
                Drawable(type='point', geometry=defect['point'], style=style)
            )

        visuals = list(vis_by_layer.values())
        if visuals:
            legend_info = mapper.get_legend_info('Unified Surface Variation (mm)')
            for vis in visuals:
                vis.metadata['legend_info'] = legend_info
        return visuals
