def debug_points(points):
    """Write points to the file"""
    import datetime

    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S_%f")
    filename = f"invalid_polygon_points_{timestamp}.txt"
    try:
        with open(filename, 'w') as f:
            f.write("# Points for invalid polygon\n")
            for point in points:
                f.write(f"{point[0]},{point[1]}\n")
        print(f"Points saved to {filename}")
    except IOError as e:
        print(f"Error writing to file {filename}: {e}")