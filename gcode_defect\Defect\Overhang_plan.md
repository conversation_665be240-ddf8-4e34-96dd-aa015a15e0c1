@Defect/Overhang.py
workflow is as follwing:
1. get the z list of model.layers: model_layers_z_list
2. get the model paras: 'support_top_z_distance'(float)
3. optimize the logic to get support tracks,
    for one layer L with Z = model_layers_z_list[k] and height H, its support tracks can be devided into two classes: 
    - all LineType.Support_interface in the layer L_s with supposed_z = Z-support_top_z_distance-layer.height
        - actually, there may not exist supposed_z, we can find nearest layer under supposed_z
    - all other LineType in support_LineType in the layer L_u with z = Z-H
4. build rtree of support tracks and continuous polygon of support tracks
5. check every outer wall and overhang wall in each layer
    - devide outer wall and overhang wall into small segments ((segments code refer to @Defect/LineShift.py))
    - build polygon of each segment
    - check how much segment polygon overlap with support polygon
6. if overlap ratio is smaller than threshold (overlap ratio), it is overhang segment
    - another criterion for identifying defects, that is use angle instead overlap ratio (angle = acrtan(segment.width-overlap_width/segment.height))
7. create defect visiualization of each overhang segment and colored base on its overlap ratio(color code refer to @Defect/LineShift.py)