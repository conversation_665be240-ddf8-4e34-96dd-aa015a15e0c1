from typing import  Any, Optional, Type

from gcode_defect.Defect.Base import DefectAnalyzerBase
from gcode_defect.GcodeParse.ModelInfo import ModelInfo

# Import all analyzer classes
from gcode_defect.Defect import Overhang, ZSeamOnOverhang, Overlap, LineShift, SurfaceVariation, LongEmptyMove, LayerTime
from gcode_defect.Defect.UnifiedSurfaceAnalyzer import UnifiedSurfaceAnalyzer

# A unique sentinel object to represent a cache miss, distinguishing it from a cached `None` value.
_CACHE_MISS = object()

class AnalysisOrchestrator:
    """
    Manages the execution of defect analyzers, handling dependencies and caching results
    to avoid redundant computations.
    """
    def __init__(self):
        # Cache for raw analysis results (output of .analysis() method)
        self._raw_results_cache: dict[tuple, Any] = {}
        
        # A map from defect_name to the actual analyzer class
        self._analyzer_classes: dict[str, Type[DefectAnalyzerBase]] = {
            "overhang": Overhang.OverhangAnalyzer,
            "z_seam_on_overhang": ZSeamOnOverhang.ZSeamOnOverhangAnalyzer,
            "overlap": Overlap.OverlapAnalyzer,
            "line_shift": LineShift.LineShiftAnalyser,
            "surface_variation": SurfaceVariation.SurfaceVariationAnalyzer,
            "unified_surface": UnifiedSurfaceAnalyzer,
            "long_empty_move": LongEmptyMove.LongEmptyMoveAnalyzer,
            "layer_time": LayerTime.LayerTimeAnalyzer,
        }

        # Explicitly define the dependencies between analyzers
        self._dependencies: dict[str, list[str]] = {
            "z_seam_on_overhang": ["overhang"],
            "surface_variation": ["line_shift"],
            "unified_surface": ["overlap", "line_shift"]
        }

    def get_analyzer_class(self, defect_name: str) -> Optional[Type[DefectAnalyzerBase]]:
        """Retrieves the class for a given defect analyzer name."""
        return self._analyzer_classes.get(defect_name)

    def create_visualization(self, defect_name: str, params: dict[str, Any], analysis_result: Optional[Any] = None) -> Any:
        """Creates visualization-ready objects from raw analysis results."""
        result_to_visualize = analysis_result

        # If no result was passed in, try to get it from the cache.
        if result_to_visualize is None:
            cache_key = self._generate_cache_key(defect_name, params)
            cached_value = self._raw_results_cache.get(cache_key, _CACHE_MISS)

            if cached_value is _CACHE_MISS:
                return None  # Analysis has not been run, so we can't visualize.

            result_to_visualize = cached_value

        # If the result is None (e.g., analysis ran and returned None), we can't visualize.
        if result_to_visualize is None:
            return None

        analyzer_instance = self.get_analyzer_instance(defect_name)
        return analyzer_instance.defect_visualization(result_to_visualize)

    def get_analyzer_instance(self, name: str) -> DefectAnalyzerBase:
        """Instantiates and returns an analyzer by its defect name."""
        analyzer_class = self._analyzer_classes.get(name)
        if not analyzer_class:
            raise ValueError(f"Analyzer '{name}' not found.")
        return analyzer_class()

    def _generate_cache_key(self, name: str, params: dict[str, Any]) -> tuple:
        """Generates a stable, hashable cache key from the analysis name and its relevant parameters."""
        analyzer_params_defs = self.get_analyzer_params(name)
        # Get the set of valid parameter IDs for the given analyzer
        param_ids = {p['id'] for p in analyzer_params_defs}

        # Filter the input params to only include those relevant to this specific analyzer
        relevant_params = {k: v for k, v in params.items() if k in param_ids}

        # Create a sorted, hashable representation of the relevant parameters
        # This ensures that {'a': 1, 'b': 2} and {'b': 2, 'a': 1} produce the same key.
        sorted_params_tuple = tuple(sorted(relevant_params.items()))

        return (name, sorted_params_tuple)

    def run_analysis(self, name: str, model: ModelInfo, params: dict[str, Any]) -> Any:
        """
        Executes a specific analysis, ensuring all its dependencies are run first.
        Results are cached based on the analysis name and its specific parameters
        to prevent re-computation if parameters haven't changed.
        """
        cache_key = self._generate_cache_key(name, params)
        cached_result = self._raw_results_cache.get(cache_key, _CACHE_MISS)
        if cached_result is not _CACHE_MISS:
            print(f"CACHE HIT: Using cached result for '{name}' with params {cache_key[1]}")
            return cached_result

        print(f"CACHE MISS: Running analysis for '{name}' with params {cache_key[1]}")
        
        precomputed_results = {}
        for dep_name in self._dependencies.get(name, []):
            print(f"-> Dependency '{dep_name}' required for '{name}'. Running it first.")
            precomputed_results[dep_name] = self.run_analysis(dep_name, model, params)
        
        analyzer_instance = self.get_analyzer_instance(name)
        result = analyzer_instance.analysis(model, precomputed=precomputed_results, **params)

        self._raw_results_cache[cache_key] = result
        return result

    def clear_cache(self):
        """Clears all cached analysis results."""
        print("Clearing analysis orchestrator cache.")
        self._raw_results_cache.clear()

    def get_analyzer_params(self, name: str) -> list:
        """Gets the parameter definitions for a given analyzer by its name."""
        analyzer_instance = self.get_analyzer_instance(name)
        if not analyzer_instance:
            return []
        return analyzer_instance.analysis_params
