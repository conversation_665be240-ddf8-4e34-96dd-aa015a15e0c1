from __future__ import annotations
from abc import ABC, abstractmethod
from typing import Generic, Any, Literal, TypeVar
from dataclasses import dataclass, field
from gcode_defect.GcodeParse.ModelInfo import ModelInfo

# Define the types of geometry that can be drawn. This provides type-hinting support.
DrawableType = Literal['point', 'track', 'polygon_2d', 'text_annotation']
@dataclass
class Drawable:
    """
    A generic container for a single piece of geometry and its associated style.
    This is the fundamental building block for all visualizations.
    """
    type: DrawableType
    geometry: Any
    style: dict[str, Any] = field(default_factory=dict)
    text: str = ""  # Used specifically for 'text_annotation' type

@dataclass
class DefectVisualization:
    """
    A standard container for all visualization data related to a single defect
    instance or a set of related defects (e.g., a continuous overhang).
    """
    # A list of all drawable components for this defect.
    drawables: list[Drawable] = field(default_factory=list)
    # Instructions to modify existing geometry (e.g., coloring entire layers).
    layer_styles: dict[int, dict[str, Any]] = field(default_factory=dict)
    # Optional metadata for display in UI text boxes or for filtering.
    metadata: dict[str, Any] = field(default_factory=dict)


T_AnalysisResult = TypeVar('T_AnalysisResult')

class DefectAnalyzerBase(ABC, Generic[T_AnalysisResult]):
    """
    An abstract base class for all defect analysis implementations.
    """
    def __init__(self):
        pass

    @property
    @abstractmethod
    def defect_name(self) -> str:
        """A unique name for this defect type."""
        raise NotImplementedError

    @property
    @abstractmethod
    def analysis_params(self) -> list[dict[str, Any]]:
        """A list of dictionaries defining the parameters for this analysis."""
        raise NotImplementedError
    
    @abstractmethod
    def analysis(self, model: ModelInfo, precomputed, **kwargs) -> T_AnalysisResult:
        """
        Perform the analysis.

        Args:
            model: The model to analyze.
            precomputed: A dictionary containing the raw results of any declared dependencies.
            **kwargs: The user-configurable parameters for this analysis.
        """
        raise NotImplementedError
    
    @staticmethod
    @abstractmethod
    def defect_visualization(analysis_result: T_AnalysisResult):
        """Return a DefectVisualization object for the analysis results."""
        raise NotImplementedError
