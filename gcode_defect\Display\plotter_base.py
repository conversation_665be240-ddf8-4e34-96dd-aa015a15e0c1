from abc import ABC, abstractmethod
from enum import Enum
from gcode_defect.GcodeParse.ModelInfo import ModelInfo, LineType, DotType, TrackInfo

# Shared color mapping
LineColor_MAP = {
    LineType.Empty_move:"#011489",
    LineType.Wipe:"#e7f35c",
    LineType.Gap_infill:"#95a05f",
    LineType.Floating_vertical_shell:"#ae0992",
    LineType.Internal_solid_infill:"#ec10c8",
    LineType.Sparse_infill:"#af2c51",
    LineType.Outer_wall:"#f6aa09",
    LineType.Inner_wall:"#dfec13",
    LineType.Overhang_wall:"#1205ff",
    LineType.Bottom_surface:"#5b16e9",
    LineType.Top_surface:"#c23d56",
    LineType.Bridge:"#157bea",
    LineType.Support:"#0cb72f",
    LineType.Support_interface:"#0b8223",
    LineType.Support_transition:"#075316",
    LineType.Brim:"#04360e",
    LineType.Custom:"#757b8a",

    DotType.Withdraw:"#B91212",
    DotType.Seam:"#666754",
    DotType.AdjustPara:"#2e2e2e",
    DotType.Reload:"#8b0775",

    "default":"#757b8a",
}

class color_mode(Enum):
    layer_based = 0
    line_type_based = 1
    line_width_based = 2
    layer_time_based = 3
    gray_based = 4

NUM_COLORS = 10  # Number of discrete colors in the gradient

def color_to_rgb(color_str):
    """Converts a hex color string to an RGB tuple (0-255)."""
    color_str = color_str.lstrip('#')
    return tuple(int(color_str[i:i+2], 16) for i in (0, 2, 4))

def linear_color_map(value, color1, color2):
    """Create a color that is linearly interpolated between two colors."""
    if color1.startswith('#') and color2.startswith('#'):
        r1, g1, b1 = color_to_rgb(color1)
        r2, g2, b2 = color_to_rgb(color2)
    else:
        raise ValueError("Color format not supported")
    
    # Quantize the value to one of NUM_COLORS discrete levels
    quantized_value = int(value * (NUM_COLORS - 1)) / (NUM_COLORS - 1)

    r = r1 + (r2 - r1) * quantized_value
    g = g1 + (g2 - g1) * quantized_value
    b = b1 + (b2 - b1) * quantized_value
    return f"#{int(r*255):02x}{int(g*255):02x}{int(b*255):02x}"

class PlotterBase(ABC):
    def __init__(self, model: ModelInfo = None):
        self.model: ModelInfo = model
        self.layer_id = None
        self.line_isPlot_MAP = None
        self.plt_layer_num = -1
        self.color_max_value = 1
        self.color_min_value = 0

    def init_widget_layout(self,parent_widget):
        """
        Robustly initialize the layout within a parent QWidget. This method is idempotent and safely handles layout management.
        """
        from PySide6.QtWidgets import QVBoxLayout
        
        # Safely get the layout from the parent widget. If it doesn't exist, create one.
        layout = parent_widget.layout()
        if not layout:
            layout = QVBoxLayout(parent_widget)
            layout.setContentsMargins(0, 0, 0, 0)
            parent_widget.setLayout(layout)
        else:
            # If a layout exists, clear any widgets it might already contain.
            while layout.count():
                item = layout.takeAt(0)
                if item.widget():
                    # Properly delete the old widget
                    item.widget().deleteLater()

    def set_model(self, model):
        self.model = model

    def set_layer(self, layer):
        self.layer = layer

    def set_layer_id(self, layer_id: int):
        self.layer_id = layer_id

    def _get_distinct_layer_color(self, layer_id):
        """
        Generate distinct colors for each layer. Using a simple HSL-like approach to cycle through hues and then converting to RGB. Avoid pure white (255,255,255) as it's the background color.
        """
        # Max number of layers to distinguish
        max_layers = len(self.model.layers) if self.model else 100 
        layer_id = layer_id % max_layers
        # Calculate hue based on layer_id, distributing evenly
        hue = (layer_id * 0.6180339887) % 1  # Golden angle approximation for good distribution

        # Fixed saturation and lightness for distinctness
        saturation = 0.7
        lightness = 0.6 

        # Convert HSL to RGB
        def hsl_to_rgb(h, s, l):
            c = (1 - abs(2 * l - 1)) * s
            x = c * (1 - abs((h * 6) % 2 - 1))
            m = l - c / 2
            
            if 0 <= h < 1/6:
                r, g, b = c, x, 0
            elif 1/6 <= h < 2/6:
                r, g, b = x, c, 0
            elif 2/6 <= h < 3/6:
                r, g, b = 0, c, x
            elif 3/6 <= h < 4/6:
                r, g, b = 0, x, c
            elif 4/6 <= h < 5/6:
                r, g, b = x, 0, c
            else:
                r, g, b = c, 0, x
            
            return ((r + m) * 255), ((g + m) * 255), ((b + m) * 255)

        r, g, b = hsl_to_rgb(hue, saturation, lightness)

        # Ensure colors are not too close to white (background)
        # If the color is too light, darken it slightly
        if r > 240 and g > 240 and b > 240:
            r -= 10
            g -= 10
            b -= 10
        
        # Convert back to hex string
        return f"#{int(r):02x}{int(g):02x}{int(b):02x}"

    def get_track_color(self, track: TrackInfo, clr_md=color_mode.line_type_based):
        """Get track color based on color mode - shared between 2D and 3D"""

        color = LineColor_MAP["default"]
        
        if clr_md == color_mode.line_type_based:
            if track.lineType in LineColor_MAP:
                color = LineColor_MAP[track.lineType]
            elif track.dotType in LineColor_MAP:
                color = LineColor_MAP[track.dotType]
        elif clr_md == color_mode.layer_based:
            color = self._get_distinct_layer_color(self.layer_id)
        elif clr_md == color_mode.layer_time_based:
            if hasattr(track, 'time') and track.time is not None:
                color = linear_color_map((track.time - self.color_min_value) / (self.color_max_value - self.color_min_value), "#C62020", "#12F801")
        elif clr_md == color_mode.line_width_based:
            if hasattr(track, 'width') and track.width is not None:
                color = linear_color_map((track.width - self.color_min_value) / (self.color_max_value - self.color_min_value), "#C62020", "#12F801")
        elif clr_md == color_mode.gray_based:
            color = "#808080"

        return color

    def should_skip_track(self, track):
        """Check if track should be skipped based on filters"""
        # Ensure filter map is initialized
        if self.line_isPlot_MAP is None:
            self.ensure_filter_map_initialized()
        
        # Check LineType filter
        if hasattr(track, 'lineType') and track.lineType in self.line_isPlot_MAP:
            if not self.line_isPlot_MAP[track.lineType]:
                return True
        
        # Check DotType filter
        if hasattr(track, 'dotType') and track.dotType in self.line_isPlot_MAP:
            if not self.line_isPlot_MAP[track.dotType]:
                return True
        
        return False

    def get_tracks_to_process(self, track_ratio = 1.0):
        """Get tracks to process based on ratio"""
        layer = self.model.layers[self.layer_id]
        total_tracks = len(layer.tracks)
        tracks_to_draw = int(total_tracks * track_ratio)
        return layer.tracks[:tracks_to_draw]

    def calculate_extreme_values(self, tracks_to_process, clr_md):
        """Calculate max time and width values for color mapping"""
        if clr_md == color_mode.layer_time_based:
            tracks_time = [track.time for track in tracks_to_process if hasattr(track, 'time') and track.time is not None]
            if tracks_time:
                self.color_max_value = max(tracks_time)
                self.color_min_value = min(tracks_time)
        
        if clr_md == color_mode.line_width_based:
            track_widths = [track.width for track in tracks_to_process if hasattr(track, 'width') and track.width is not None]
            if track_widths:
                self.color_max_value = max(track_widths)
                self.color_min_value = min(track_widths)

        return self.color_max_value, self.color_min_value
                
    def ensure_filter_map_initialized(self):
        """Ensure line_isPlot_MAP is initialized to prevent filtering all tracks"""
        if self.line_isPlot_MAP is None:
            from gcode_defect.GcodeParse.ModelInfo import LineType, DotType
            self.line_isPlot_MAP = {
                # Enable all line types by default
                LineType.Empty_move: True,
                LineType.Wipe: True,
                LineType.Gap_infill: True,
                LineType.Floating_vertical_shell: True,
                LineType.Internal_solid_infill: True,
                LineType.Sparse_infill: True,
                LineType.Outer_wall: True,
                LineType.Inner_wall: True,
                LineType.Overhang_wall: True,
                LineType.Bottom_surface: True,
                LineType.Top_surface: True,
                LineType.Bridge: True,
                LineType.Support: True,
                LineType.Support_interface: True,
                LineType.Support_transition: True,
                LineType.Brim: True,
                LineType.Custom: True,
                # Enable all dot types by default
                DotType.Withdraw: True,
                DotType.Seam: True,
                DotType.AdjustPara: True,
                DotType.Reload: True,
            }

    @abstractmethod
    def plot_one_layer(self):
        """Plot one layer"""
        pass

    @abstractmethod
    def init_widget(self,parent_widget):
        """Initialize plotter in widget"""
        pass

    @abstractmethod
    def clear_canvas(self):
        """Clear the canvas"""
        pass
