from dataclasses import dataclass
from typing import Any, Optional
import numpy as np
import math
from shapely.ops import unary_union
from shapely.geometry import Polygon
from rtree import index

from gcode_defect.GcodeParse.ModelInfo import LayerInfo, LineType, ModelInfo, TrackInfo, MotionType
from gcode_defect.Defect.Base import DefectAnalyzerBase, DefectVisualization, Drawable
from gcode_defect.utils.spatial_indexing import build_rtree_from_geometries
from gcode_defect.utils.visualization import ColorMapper
from gcode_defect.utils.line_segment import LineSegment, discretize_tracks

# All types that can provide physical support on the layer below.
SUPPORT_PROVIDING_TYPES = [
    LineType.Outer_wall,
    LineType.Inner_wall,
    LineType.Overhang_wall,
    LineType.Bottom_surface, 
    LineType.Top_surface, 
    LineType.Brim
]

@dataclass
class OverhangSegment:
    """A container for the analysis result of a single wall segment."""
    layer_id: int
    segment_geom: LineSegment
    overlap_ratio: float
    overhang_angle_deg: float
    use_angle: bool = False
    support_intersection: Optional[Polygon] = None
    supporting_polygons: Optional[Polygon] = None

class OverhangAnalyzer(DefectAnalyzerBase[list[OverhangSegment]]):
    @property
    def defect_name(self) -> str:
        return "overhang"
    
    @property
    def analysis_params(self) -> list[dict[str, Any]]:
        """
        Args:
            segment_samp_length: The length to discretize wall tracks into smaller segments for analysis.
            min_overlap_ratio: The minimum overlap ratio below which a segment is considered an overhang.
            max_overhang_angle_deg: The maximum angle (in degrees) of the unsupported portion of a segment.
            max_overhang_length: The minimum continuous length of combined overhang segments to be flagged as a defect.
            least_support_length: The length of subsequent supported track required to "end" a sequence of overhangs.
            contour_corner_angle_deg: The angle to detect sharp corners in an overhang's path.
            look_ahead_distance: The distance to look forward/backward to calculate corner angles.
        """
        return [
            {'name': '线段采样长度 (mm)', 'id': 'segment_samp_length', 'type': 'float', 'default': 1.0},
            {'name': '最小重叠比例', 'id': 'min_overlap_ratio', 'type': 'float', 'default': 0.2},
            {'name': '使用角度阈值', 'id': 'use_angle_threshold', 'type': 'bool', 'default': True},
            {'name': '最大悬垂角度 (deg)', 'id': 'max_overhang_angle_deg', 'type': 'float', 'default': 30.0},
            {'name': '最长悬垂长度 (mm)', 'id': 'max_overhang_length', 'type': 'float', 'default': 40.0},
            {'name': '最短支撑长度 (mm)', 'id': 'least_support_length', 'type': 'float', 'default': 0.5},
            {'name': '悬垂轮廓允许拐角 (deg)', 'id': 'contour_corner_angle_deg', 'type': 'float', 'default': 150.0},
            {'name': '拐角过滤前瞻距离 (mm)', 'id': 'look_ahead_distance', 'type': 'float', 'default': 1.5}
        ]

    def analysis(self, model: ModelInfo, precomputed: dict[str, Any], **kwargs) -> list[OverhangSegment]:
        """
        Detect overhang defects by analyzing layer-to-layer wall overlap.
        Returns: A dictionary containing defect data and analysis parameters.
        """
        params = {p['id']: p['default'] for p in self.analysis_params}
        params.update(kwargs)

        defects_by_layer: list[OverhangSegment] = []

        for layer_id, layer in enumerate(model.layers):
            if layer_id % 10 == 0:
                print(f"Layer {layer_id} overhang defect analysis started")

            # Layer 0 and 1 are supported by the print bed and have no overhangs
            if layer_id == 0 or layer_id == 1:
                continue

            # Retrieve only the pre-unioned support polygon (rtree/polygons cached internally if needed)
            support_union = get_support_polygons(layer_id, model)[2]

            tracks_to_check = [t for t in layer.tracks if t.lineType in [LineType.Overhang_wall, LineType.Outer_wall]]
            wall_segments: list[LineSegment] = discretize_tracks(tracks_to_check, params['segment_samp_length'])

            cumulative_support_length = 0
            candidate_overhang_list = []

            for segment in wall_segments:
                analysis_result = self._analyze_segment_overhang(segment, support_union, params)

                if analysis_result:
                    candidate_overhang_list.append(analysis_result)
                    cumulative_support_length = 0
                elif candidate_overhang_list:
                    cumulative_support_length += segment.length

                # If a sufficient support section is found OR we have reached the end of the wall, process the preceding overhangs.
                if cumulative_support_length > params['least_support_length'] or segment is wall_segments[-1]:
                    overhang_segments = self._filter_candidate_defects(candidate_overhang_list, params)
                    if overhang_segments:
                        defects_by_layer.extend(overhang_segments)
                    cumulative_support_length = 0
                    candidate_overhang_list = []

        return defects_by_layer

    def _analyze_segment_overhang(self, segment: LineSegment, support_union: Polygon | None, params: dict) -> OverhangSegment | None:
        """Analyzes a single segment for overhang properties using a pre-unioned support polygon for speed."""
        from gcode_defect.utils.poly import build_line_width_polygon
        
        polygon = build_line_width_polygon(segment.start, segment.end, segment.updated_width)
        if not polygon:
            return None

        if support_union is not None and not support_union.is_empty:
            inter = polygon.intersection(support_union)
            overlap_ratio = inter.area / polygon.area if not inter.is_empty else 0.0
        else:
            overlap_ratio = 0.0

        # Calculate overhang angle based on the area equivalent width
        unsupported_width = segment.initial_width * (1 - overlap_ratio)
        segment_height = segment.origin_track.height
        overhang_angle_deg = 90 - math.degrees(math.atan2(unsupported_width, segment_height)) if segment_height > 1e-6 else 90.0

        # Determine if the segment is an overhang based on the overhang_angle or overlap_ratio
        is_overhang = (params['use_angle_threshold'] and overhang_angle_deg < params['max_overhang_angle_deg']) or \
                     (not params['use_angle_threshold'] and overlap_ratio < params['min_overlap_ratio'])

        if is_overhang:
            return OverhangSegment(
                segment_geom=segment,
                overlap_ratio=overlap_ratio,
                overhang_angle_deg=overhang_angle_deg,
                use_angle=params['use_angle_threshold'],
                support_intersection=inter,
                supporting_polygons=support_union,
                layer_id=segment.origin_track.layer_id
            )
        else:
            return None

    def _filter_candidate_defects(self, candidates: list[OverhangSegment], params: dict) -> list[OverhangSegment] | None:
        """Checks if a list of candidate overhangs constitutes a defect and adds it to the results."""
        from gcode_defect.utils.filters import find_sharp_corners_pts

        if not candidates:
            return None

        pts = [c.segment_geom.start for c in candidates] + [candidates[-1].segment_geom.end]
        has_sharp_corners = find_sharp_corners_pts(pts, params['contour_corner_angle_deg'], params['look_ahead_distance'])
        cumulative_length = sum(c.segment_geom.length for c in candidates)

        if has_sharp_corners or cumulative_length > params['max_overhang_length']:
            return candidates
        else:
            return None

    @staticmethod
    def defect_visualization(analysis_result: list[OverhangSegment]):
        """Helper function to add overhang drawable objects to a visualization."""
        if not analysis_result:
            return []
        
        use_angle_threshold = analysis_result[0].use_angle
        vis_by_layer: dict[int, DefectVisualization] = {}

        if use_angle_threshold:
            all_values = [seg.overhang_angle_deg for seg in analysis_result ]
            legend_title = '悬垂角度 (deg)'
            text_format = "A: {value:.1f}°"
        else:
            all_values = [seg.overlap_ratio for seg in analysis_result]
            legend_title = '重叠比例'
            text_format = "R: {value:.2f}"

        if not all_values:
            return []

        COLOR_GRADIENT = [
            '#a50026', '#d73027', '#f46d43', '#fdae61', '#fee090',
            '#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695'
        ]

        # --- 1. Use ColorMapper to handle normalization and legend ---
        mapper = ColorMapper(COLOR_GRADIENT)
        mapper.fit(all_values)

        for overhang_segment in analysis_result:
            layer_id = overhang_segment.layer_id
            if layer_id not in vis_by_layer:
                vis_by_layer[layer_id] = DefectVisualization(metadata={'layer_id': layer_id, 
                                                                       'legend_info': mapper.get_legend_info(legend_title)})

            segment = overhang_segment.segment_geom
            intersection = overhang_segment.support_intersection
            supporting_polygons = overhang_segment.supporting_polygons

            if use_angle_threshold:
                value_to_viz = overhang_segment.overhang_angle_deg
            else:
                value_to_viz = overhang_segment.overlap_ratio

            # Create a temporary TrackInfo for the segment to draw it
            z = segment.origin_track.stPos[2]
            segment_as_track = TrackInfo(st_pos=(*segment.start, z), end_pos=(*segment.end, z))
            segment_as_track.motionType = MotionType.G1

            # --- Get color from the mapper ---
            defect_color = mapper.get_color(value_to_viz)

            vis_by_layer[layer_id].drawables.append(Drawable(type='track', geometry=segment_as_track, style={'color': defect_color, 'linewidth': 2.5}))

            if supporting_polygons:
                vis_by_layer[layer_id].drawables.append(Drawable(type='polygon_2d', geometry=supporting_polygons, style={'facecolor': 'lightblue', 'edgecolor': 'blue', 'alpha': 0.3, 'zorder': 14}))

            if intersection:
                vis_by_layer[layer_id].drawables.append(Drawable(type='polygon_2d', geometry=intersection, style={'facecolor': defect_color, 'edgecolor': 'darkgreen', 'alpha': 0.5, 'zorder': 15}))

        return list(vis_by_layer.values())if vis_by_layer else []
    

def _build_continuous_polygons_from_layer(layer: LayerInfo, types_to_check: list[LineType]) -> list[Polygon]:
    """
    Groups continuous tracks of specified types within a layer and builds larger polygons for them
    using `continious_line_width_model`.
    """
    from gcode_defect.utils.poly import continious_line_width_model

    all_polygons = []
    if not layer.tracks:
        return all_polygons

    continuous_pts = []
    continuous_widths = []

    for track in layer.tracks:
        # A non-extruding move or a track of the wrong type breaks the continuity.
        is_valid_support_track = track.lineType in types_to_check and track.ex > 0

        if not is_valid_support_track:
            if continuous_pts:
                polys = continious_line_width_model(continuous_pts, continuous_widths)
                if polys:
                    all_polygons.extend(polys)
                continuous_pts, continuous_widths = [], []
            continue

        if not continuous_pts:
            continuous_pts.append(track.stPos[:2])
            continuous_widths.append(track.width)

        if track.motionType == MotionType.G1:
            continuous_pts.append(track.endPos[:2])
            continuous_widths.append(track.width)
        elif track.motionType in [MotionType.G2, MotionType.G3]:
            arc_points = track.get_arc_sample_paras()['sample_points']
            continuous_pts.extend([p[:2] for p in arc_points[1:]])
            continuous_widths.extend([track.width] * (len(arc_points) - 1))

    if continuous_pts:
        polys = continious_line_width_model(continuous_pts, continuous_widths)
        if polys:
            all_polygons.extend(polys)

    return all_polygons


def get_support_polygons(current_layer_idx: int, model: ModelInfo) -> tuple[Optional[index.Index], list[Polygon], Optional[Polygon]]:
    """
    Collects all tracks that can provide support for a given layer and builds continuous polygons.
    Returns (rtree, polygons, union) where union is a precomputed unary_union for fast overlap checks.
    """
    # Per-layer cache to avoid recomputation across iterations/runs
    if not hasattr(model, '_support_cache'):
        model._support_cache = {}
    if current_layer_idx in model._support_cache:
        return model._support_cache[current_layer_idx]

    # Cache the Z-heights of all layers as a NumPy array for efficient lookups.
    if not hasattr(model, '_z_list_cache'):
        model._z_list_cache = np.array([layer.z for layer in model.layers])
    z_list = model._z_list_cache
    support_polygons = []

    support_interface_layer_idx = get_support_layer_idx(current_layer_idx, model)
    if support_interface_layer_idx != -1:
        interface_layer = model.layers[support_interface_layer_idx]
        support_polygons.extend(_build_continuous_polygons_from_layer(interface_layer, [LineType.Support_interface, LineType.Support]))

    current_layer = model.layers[current_layer_idx]
    under_layer_z = current_layer.z - current_layer.height
    matches = np.where(np.isclose(z_list, under_layer_z))[0]
    under_layer_idx = matches[0] if len(matches) > 0 else -1
    if under_layer_idx != -1:
        under_layer = model.layers[under_layer_idx]
        support_polygons.extend(_build_continuous_polygons_from_layer(under_layer, SUPPORT_PROVIDING_TYPES))

    final_polygons = [p for p in support_polygons if p and not p.is_empty]
    rtree = build_rtree_from_geometries(final_polygons) if final_polygons else None
    support_union = unary_union(final_polygons) if final_polygons else None

    model._support_cache[current_layer_idx] = (rtree, final_polygons, support_union)
    return model._support_cache[current_layer_idx]


def get_support_layer_idx(current_layer_idx, model: ModelInfo):
    """
    Locates the layer index for support interface structures. The target Z is calculated as
    `current_layer.z - support_top_z_distance`, and this function finds the layer at or below that Z.
    """
    # Cache layer Z list on the model for repeated calls
    if not hasattr(model, '_layers_z_cache'):
        model._layers_z_cache = [layer.z for layer in model.layers]
    model_layers_z_list = model._layers_z_cache

    support_top_z_distance = model.get_one_para('support_top_z_distance') or 0.0

    if support_top_z_distance < 0:
        return -1

    supposed_z = model.layers[current_layer_idx].z - model.layers[current_layer_idx].height - support_top_z_distance

    if supposed_z < model.layers[0].z:
        return -1

    support_interface_layer_idx = -1
    for idx, z in enumerate(model_layers_z_list):
        if z - supposed_z > 1e-4:  # supposed_z can be affected by float precision
            break
        support_interface_layer_idx = idx

    return support_interface_layer_idx