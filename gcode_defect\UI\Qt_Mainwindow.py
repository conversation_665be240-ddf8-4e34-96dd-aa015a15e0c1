# Standard library imports
import traceback
from enum import Enum
from pathlib import Path
from typing import Optional

# Third-party imports
import matplotlib
from PySide6.QtCore import QCoreApplication, QObject
from PySide6.QtGui import QDoubleValidator, QIntValidator
from PySide6.QtUiTools import QUiLoader
from PySide6.QtWidgets import QCheckBox, QFileDialog, QLineEdit, QRadioButton, QTableWidgetItem

# Local application imports
from gcode_defect.Defect.AnalysisOrchestrator import AnalysisOrchestrator
from gcode_defect.Display.plotter2D import plotter2D
from gcode_defect.Display.plotter3D_pyvista import Plotter3DPyVista
from gcode_defect.Display.plotter_base import PlotterBase, color_mode
from gcode_defect.GcodeParse import DotType, GCodeProcess, LineType, ModelInfo
from gcode_defect.UI import style

class Qt_Mainwindow(QObject):
# Init and connections
    def __init__(self):
        super().__init__()
        
        # Construct path to UI file relative to this script's location for robustness
        ui_file_path = Path(__file__).parent / 'main_ui.ui'
        self.ui = QUiLoader().load(str(ui_file_path))
        # style.apply_style(self.ui)

        self.model = ModelInfo()
        self.plt_3D = Plotter3DPyVista()
        self.plt_2D = plotter2D()

        # A cache to store the VISUALIZATION results of defect analysis.
        self.defect_results_cache = {}
        self.orchestrator = AnalysisOrchestrator()

        self._switch_plt_mode('2D')
        self.init_plotter_connection()
        self._init_defect_analyzers()
        self._update_defect_parameter_table()

        # Assign a custom close event handler. Using a lambda ensures that `self`
        # refers to the Qt_Mainwindow instance, not the UI widget, which is crucial
        # for accessing controller-level attributes like `self.plt_3D`.
        self.ui.closeEvent = lambda event: self.closeEvent(event)

        # Connect all UI events
        self.ui.combo_plot_layerNum.currentIndexChanged.connect(self.on_combo_plot_layerNum_changed)
        self.ui.pushButton_chooseFile.clicked.connect(self.on_pushButton_chooseFile_clicked)
        self.ui.pushButton_importFile.clicked.connect(self.on_pushButton_importFile_clicked)
        self.ui.pushButton_plot_2D.clicked.connect(self.on_pushButton_plot2D_clicked)
        self.ui.pushButton_plot_3D.clicked.connect(self.on_pushButton_plot3D_clicked)
        self.ui.pushButton_plot_clf.clicked.connect(self.on_pushButton_plot_clf_clicked)
        self.ui.pushButton_analyseDefect.clicked.connect(self.on_pushButton_analyseDefect_clicked)
        self.ui.verticalSlider_plot_layerNum.valueChanged.connect(self.on_verticalSlider_plot_layerNum_valueChanged)
        self.ui.lineEdit_xlim_l.editingFinished.connect(self.on_lineEdit_lim_textChanged)
        self.ui.lineEdit_xlim_h.editingFinished.connect(self.on_lineEdit_lim_textChanged)
        self.ui.lineEdit_ylim_l.editingFinished.connect(self.on_lineEdit_lim_textChanged)
        self.ui.lineEdit_ylim_h.editingFinished.connect(self.on_lineEdit_lim_textChanged)
        self.ui.horizontalScrollBar_plot_trackNum.valueChanged.connect(self.on_plot_settings_changed)
        self.ui.horizontalScrollBar_transparant.valueChanged.connect(self.on_plot_settings_changed)

    def show(self):
        """Shows the main window, making the controller class callable from main.py like a QWidget."""
        self.ui.show()

    def init_plotter_connection(self):
        # Create mapping between LineType and corresponding checkbox names
        self.is_plot_checkbox = {LineType: "checkBox_isPlot_" + LineType.value.replace(" ", "_") for LineType in LineType}
        self.is_plot_checkbox |= {DotType: "checkBox_isPlot_" + DotType.value.replace(" ", "_") for DotType in DotType}
        
        # Connect checkboxes to plot function
        other_checkboxes = [
            "checkBox_isPlot_line_width",
            "checkBox_isPlot_defect"
        ]
        checkboxes = other_checkboxes + list(self.is_plot_checkbox.values())
        for checkbox in checkboxes:
            cb = getattr(self.ui, checkbox)
            cb.stateChanged.connect(self.on_plot_settings_changed)
        radioButtons=[
            "radioButton_plot_color_layer",
            "radioButton_plot_color_type",
            "radioButton_plot_color_width",
            "radioButton_plot_color_time",
            "radioButton_plot_color_gray"
        ]
        for radioButton in radioButtons:
            rb = getattr(self.ui, radioButton)
            rb.clicked.connect(self.on_plot_settings_changed)
        
        # Return initial map (also used for updates)
        self.plt_2D.line_isPlot_MAP = self._update_line_isPlot_MAP()

    def _init_defect_analyzers(self):
        """Initializes and connects defect analyzer radio buttons."""
        # Map radio buttons to the string keys used by the AnalysisOrchestrator.
        # This decouples the UI from the specific analyzer classes.
        self.defect_radio_map = {
            self.ui.radioButton_longEmptyDist: "long_empty_move",
            self.ui.radioButton_defect_overlap: "overlap",
            self.ui.radioButton_defect_overhang: "overhang",
            self.ui.radioButton_defect_layerTimeDiff: "layer_time",
            self.ui.radioButton_overhang_zseam: "z_seam_on_overhang",
            self.ui.radioButton_shiftLine: "line_shift",
            self.ui.radioButton_surface: "surface_variation",
            self.ui.radioButton_unified_surface: "unified_surface",
        }
        for radio_button in self.defect_radio_map.keys():
            radio_button.setChecked(False)
            radio_button.clicked.connect(self._update_defect_parameter_table)

# Slot functions
    def on_pushButton_chooseFile_clicked(self):
        """Open file dialog to choose G-code file"""
        exist_file_path = self.ui.lineEdit_filePath.text() or "E:\模型\gcode和缺陷模型"
        filePath, _ = QFileDialog.getOpenFileName(
            self.ui,  # 父窗口对象
            "选择你要上传的gcode文件",  # 标题
            exist_file_path,  # 起始目录
            "文本类型 (*.txt *.gcode)"  # 选择类型过滤项，过滤内容在括号中
        )
        if filePath:
            self.ui.lineEdit_filePath.setText(filePath)

    def on_pushButton_importFile_clicked(self):
        """Import G-code file and setup plotters"""
        file_path = self.ui.lineEdit_filePath.text()
        if not file_path:
            self.ui.statusbar.showMessage("请先选择文件", 2000)
            return

        try:
            try:
                chord_error_mm = float(self.ui.lineEdit_chord_error.text())
            except ValueError:
                self.ui.statusbar.showMessage("Invalid chord error. Using default 0.01mm.", 3000)
                print("Error Input: chord_error_mm should be a float. Now use default parameter 0.01mm.")
                chord_error_mm = 0.01

            # Clear all caches when a new file is loaded
            self.orchestrator.clear_cache()
            self.defect_results_cache.clear()
            self.model = GCodeProcess(file_path).g_file2model(chord_error_mm=chord_error_mm, max_discrete_num=50)

            # pre calculate
            for layer in self.model.layers:
                layer.extract_outer_inner_wall_contour()
                layer.get_z_seam_pos_list()

            self.model.update_layer_height()
            self.model.update_track_width()
            self.model.store_discrete_data()
            # Set model for both plotters
            self.plt_3D.set_model(self.model)
            self.plt_2D.set_model(self.model)
            layer_num = len(self.model.layers) - 1

            # Setup model info display
            self.ui.label_model_info_display.setText(
                f"模型层数：{self.model.paras['total layer number']}\n"
                f"Bambu Studio：{self.model.paras['BambuStudio']}\n"
                f"喷嘴直径：{self.model.paras['nozzle_diameter']}\n"
                f"G1/2/3指令数目:{len([t for layer in self.model.layers for t in layer.tracks])}"
            )

            # Setup UI controls
            self.ui.combo_plot_layerNum.clear()
            self.ui.combo_plot_layerNum.addItems([str(i) for i in range(layer_num + 1)])
            self.ui.verticalSlider_plot_layerNum.setMinimum(0)
            self.ui.verticalSlider_plot_layerNum.setMaximum(layer_num)
            self.ui.verticalSlider_plot_layerNum.setValue(1)

            self.ui.statusbar.showMessage("✅ 模型已导入", 2000)
        except KeyError as e:
            msg = f"Model info is incomplete. Missing key: {e}"
            print(msg)
            traceback.print_exc()
            self.ui.statusbar.showMessage(f"模型信息不完整: 缺少 {e}", 4000)
        except Exception as e:
            msg = f"Error importing file: {e}"
            print(msg)
            traceback.print_exc()
            self.ui.statusbar.showMessage("❌ 文件导入失败", 4000)

    def on_combo_plot_layerNum_changed(self):
        self.ui.verticalSlider_plot_layerNum.setValue(self.ui.combo_plot_layerNum.currentIndex())
    
    def on_pushButton_plot2D_clicked(self):
        """Handle 2D plot button"""
        self._initiate_plot('2D')

    def on_pushButton_plot3D_clicked(self):
        """Handle 3D plot button"""
        self._initiate_plot('3D')

    def _initiate_plot(self, mode: str):
        """Switches to the specified plot mode and triggers a plot."""
        try:
            # Switch mode if necessary
            if not hasattr(self, 'current_plot_mode') or self.current_plot_mode != mode:
                self._switch_plt_mode(mode)
            
            # Verify the switch was successful
            if self.current_plot_mode != mode:
                self.ui.statusbar.showMessage(f"无法初始化{mode}模式", 2000)
                return
            
            # Mode-specific preparations
            if mode == '3D':
                self.ui.horizontalScrollBar_plot_trackNum.setValue(self.ui.horizontalScrollBar_plot_trackNum.maximum())
            
            self._plotter_plot()

        except Exception as e:
            print(f"Error in {mode} plotting: {e}")
            traceback.print_exc()
            self.ui.statusbar.showMessage(f"❌ {mode}绘制失败", 2000)

    def on_pushButton_analyseDefect_clicked(self):
        defect_name = self._get_selected_defect_name()
        if defect_name is None:
            self.ui.statusbar.showMessage("Please select a defect type to analyze.", 2000)
            return

        self.ui.statusbar.showMessage(f"Analyzing {defect_name}...", 3000)
        
        # Get parameters from the UI table
        table = self.ui.tableWidget_defectParams
        params = {}
        for row in range(table.rowCount()):
            widget = table.cellWidget(row, 1)
            param_id = table.item(row, 2).text()
            param_type = table.item(row, 3).text()
            if isinstance(widget, QLineEdit):
                params[param_id] = float(widget.text()) if param_type == 'float' else int(widget.text())
            elif isinstance(widget, QCheckBox):
                params[param_id] = widget.isChecked()

        # Use the orchestrator to run the analysis and handle dependencies/caching
        raw_analysis_result = self.orchestrator.run_analysis(defect_name, self.model, params)
        # The orchestrator is now also responsible for creating the visualization objects
        visualization_result = self.orchestrator.create_visualization(defect_name, params, raw_analysis_result)
        self.defect_results_cache[defect_name] = visualization_result

        # Determine the number of defects/layers for the UI message
        result_count = 0
        if isinstance(raw_analysis_result, (list, dict)):
            result_count = len(raw_analysis_result)

        # Update UI with results
        self.ui.statusbar.showMessage(f"✅ {defect_name} analysis complete. Found {result_count} defects/layers.", 3000)
        self.ui.textEdit_defect_display.setText(f"Found {result_count} {defect_name} defects/layers.")

    def on_pushButton_plot_clf_clicked(self):
        """Clear current plot"""
        plotter = self._get_current_plotter()
        plotter.clear_canvas()
        self.ui.statusbar.showMessage("✅ 画布已清空", 1000)
     
    def on_plot_settings_changed(self):
        """
        Handles changes from any plot settings widget.
        In 2D mode, this always triggers a full replot.
        In 3D mode, it attempts a fast, incremental update for visibility and color changes,
        falling back to a full replot for significant changes (like transparency) or if the
        initial plot hasn't been rendered yet.
        This is the central slot for most interactive plot controls.
        """
        plotter = self._get_current_plotter()
        if plotter is None:
            return

        # In 2D mode, always do a full replot as there's no fast update path.
        if self.current_plot_mode == '2D':
            self._plotter_plot()
            return

        # --- 3D Mode Update Logic ---
        can_fast_update = hasattr(plotter, '_actors_by_type') and plotter._actors_by_type
        sender = self.sender()

        is_linetype_visibility_change = (isinstance(sender, QCheckBox) and sender.objectName() in self.is_plot_checkbox.values())
        is_defect_visibility_change = (sender is self.ui.checkBox_isPlot_defect)
        is_color_change = isinstance(sender, QRadioButton)

        # A full replot is needed for sliders, line width, or if fast update is not possible.
        if not can_fast_update or not (is_linetype_visibility_change or is_defect_visibility_change or is_color_change):
            self._plotter_plot()
            return

        # --- Fast Update Path for 3D ---
        if is_linetype_visibility_change:
            print("Fast path: Updating visibility.")
            plotter.line_isPlot_MAP = self._update_line_isPlot_MAP()
            plotter.update_visibility()
        elif is_defect_visibility_change:
            print("Fast path: Toggling defect visibility.")
            defects_to_plot = self._get_defects_to_plot()
            plotter.plot_defects(defects_to_plot)
        elif is_color_change:
            print("Fast path: Updating color.")
            clr_md = self._get_color_mode()
            defects_to_plot = self._get_defects_to_plot()
            plotter.update_color(clr_md, defects=defects_to_plot)

    def on_verticalSlider_plot_layerNum_valueChanged(self):
        # Update combo box to match slider
        self.ui.combo_plot_layerNum.setCurrentIndex(self.ui.verticalSlider_plot_layerNum.value())
        if self.current_plot_mode == '2D' or (self.current_plot_mode == '3D' and self.ui.radioButton_plt_one_layer_3D.isChecked()):
            self._plotter_plot()

    def on_lineEdit_lim_textChanged(self):
        """Handle axis limit changes for both 2D and 3D modes"""
        if self.current_plot_mode == '2D':
            # Handle 2D axis limits
            current_lims = self.plt_2D.ax.axis()
            txt_lims = [
                self.ui.lineEdit_xlim_l.text(),  # x下限
                self.ui.lineEdit_xlim_h.text(),  # x上限
                self.ui.lineEdit_ylim_l.text(),  # y下限
                self.ui.lineEdit_ylim_h.text()  # y上限
            ]
            try:
                float_lims = [
                    float(txt_lim) if txt_lim.strip() != "" else current_lims[i]
                    for i, txt_lim in enumerate(txt_lims)
                ]
            except ValueError:
                print("请输入有效的数字")
                return
            self.plt_2D.ax.axis(float_lims)
            self.plt_2D.canvas.draw_idle()

#Private functions
    def _get_color_mode(self):
        """Get color mode based on UI radio button states"""
        if self.ui.radioButton_plot_color_layer.isChecked():
            return color_mode.layer_based
        elif self.ui.radioButton_plot_color_type.isChecked():
            return color_mode.line_type_based
        elif self.ui.radioButton_plot_color_width.isChecked():
            return color_mode.line_width_based
        elif self.ui.radioButton_plot_color_time.isChecked():
            return color_mode.layer_time_based
        elif self.ui.radioButton_plot_color_gray.isChecked():
            return color_mode.gray_based
        else:
            return color_mode.line_type_based      
 
    def _get_selected_defect_name(self) -> Optional[str]:
        """Gets the string key for the currently selected defect analyzer."""
        for radio_button, analyzer_name in self.defect_radio_map.items():
            if radio_button.isChecked():
                return analyzer_name
        return None
         
    def _get_defects_to_plot(self) -> list:
        """
        Helper function to get the current defect visualization data based on UI state.
        Filters defects for the current layer if in a single-layer view.
        If `for_coloring` is False, it also checks if the defect plot checkbox is checked.
        """
        if not self.ui.checkBox_isPlot_defect.isChecked():
            return []

        defect_name_to_plot = self._get_selected_defect_name()
        cached_result = self.defect_results_cache.get(defect_name_to_plot)
        if not cached_result:
            cached_result = self.orchestrator.create_visualization(defect_name_to_plot)

        # 'layer_time' is a special case that applies styles to layers, not individual drawables.
        if defect_name_to_plot == "layer_time":
            return [cached_result] if cached_result else []

        # Determine if we are in a single-layer view mode
        is_single_layer_view = (self.current_plot_mode == '2D' or
                                (self.current_plot_mode == '3D' and self.ui.radioButton_plt_one_layer_3D.isChecked()))

        if not is_single_layer_view:
            return cached_result if isinstance(cached_result, list) else [cached_result]

        # Single layer view, filter defects by the current layer ID
        current_layer_id = self.ui.combo_plot_layerNum.currentIndex()
        defects_for_layer = []

        if isinstance(cached_result, list):
            for vis_obj in cached_result:
                if vis_obj.metadata and vis_obj.metadata.get('layer_id') == current_layer_id:
                    defects_for_layer.append(vis_obj)
        elif isinstance(cached_result, dict):
            if current_layer_id in cached_result:
                defects_for_layer = cached_result[current_layer_id]
        
        return defects_for_layer

    def _switch_plt_mode(self, mode: str):
        """Switch plot mode to 2D or 3D"""
        if hasattr(self, 'current_plot_mode') and self.current_plot_mode == mode:
            return

        # Before switching, if the current mode is 3D, we must explicitly
        # close the plotter to release its OpenGL context before it's destroyed.
        if hasattr(self, 'current_plot_mode') and self.current_plot_mode == '3D':
            print("Switching away from 3D mode. Closing 3D plotter to release resources.")
            self.plt_3D.close()

        self.current_plot_mode = mode

        plotter = self._get_current_plotter()
        try:
            plotter.init_widget(self.ui.matplotlib_area)  
            print(f"Switched to {mode} mode")
        except Exception as e:
            print(f"Failed to switch to {mode} mode: {e}")
            if mode != '2D':
                self._switch_plt_mode('2D')

    def _get_current_plotter(self) -> Optional[PlotterBase]:
        """Get the current plotter based on mode"""
        if hasattr(self, 'current_plot_mode') and self.current_plot_mode == '2D':
            return self.plt_2D
        elif hasattr(self, 'current_plot_mode') and self.current_plot_mode == '3D':
            return self.plt_3D
        return None

    def _update_line_isPlot_MAP(self) -> dict:
        # Build the map dynamically based on current checkbox states
        line_isPlot_MAP = {
            type: getattr(self.ui, checkbox_name).isChecked() 
            for type, checkbox_name in self.is_plot_checkbox.items()
        }
        return line_isPlot_MAP

    def _validate_model(self) -> bool:
        """Validate that model is loaded and ready"""
        # Check if model is loaded
        if not hasattr(self, 'model') or self.model is None or not hasattr(self.model, 'layers') or len(self.model.layers) == 0:
            print("Model not loaded or invalid")
            self.ui.statusbar.showMessage("请导入正确的模型", 2000)
            return False
        return True

    def _validate_combo_plot_layer_num(self) -> bool:
        """Validate that layer number is valid"""
        # Check if layer number is valid
        layer_num = self.ui.combo_plot_layerNum.currentIndex()
        if self.ui.combo_plot_layerNum.count() == 0:
            print(f"No layer numbers in combo box")
            self.ui.statusbar.showMessage("没有可用层数", 2000)
            return False
        elif layer_num < 0 or layer_num >= len(self.model.layers):
            print(f"Invalid layer number: {layer_num}, total layers: {len(self.model.layers)}")
            self.ui.statusbar.showMessage("没有可用层数", 2000)
            return False
        return True

    def _plotter_plot(self):
        """Plot using the current plotter"""
        # Get current plotter
        plotter = self._get_current_plotter()
        if plotter is None:
            return
        
        # Check if model is loaded and layer number is valid
        if not self._validate_model() or not self._validate_combo_plot_layer_num():
            return
        layer_num = self.ui.combo_plot_layerNum.currentIndex()
        plotter.set_layer_id(layer_num)
        plotter.set_model(self.model)

        # Update filter map if available
        if hasattr(plotter, 'line_isPlot_MAP'):
            plotter.line_isPlot_MAP = self._update_line_isPlot_MAP()
        
        # Update plot settings
        clr_md = self._get_color_mode()
        defects_to_plot = self._get_defects_to_plot()
        track_ratio = self.ui.horizontalScrollBar_plot_trackNum.value() / self.ui.horizontalScrollBar_plot_trackNum.maximum()
        alpha = self.ui.horizontalScrollBar_transparant.value() / self.ui.horizontalScrollBar_transparant.maximum()
        
        is_draw_width = self.ui.checkBox_isPlot_line_width.isChecked()
        is_clear_previous = not self.ui.checkBox_isPlot_layer_overlap.isChecked()
        
        # --- 1. Plot base model or one layer ---
        if self.current_plot_mode =='3D':
            if self.ui.radioButton_plt_model_3D.isChecked():
                plotter.plot_model(clr_md=clr_md, alpha=alpha, defects=defects_to_plot)
            elif self.ui.radioButton_plt_one_layer_3D.isChecked():
                plotter.plot_one_layer(clr_md=clr_md, alpha=alpha, is_clear_previous=is_clear_previous, defects=defects_to_plot)
        
        elif self.current_plot_mode =='2D':
            plotter.plot_one_layer(clr_md=clr_md,
                                    alpha=alpha,
                                    is_draw_width=is_draw_width,
                                    track_ratio=track_ratio,
                                    is_clear_previous=is_clear_previous,
                                    defects=defects_to_plot)
        
        # --- 2. Universal Defect Plotting ---
        defects_to_plot = self._get_defects_to_plot()
        if defects_to_plot:
            plotter.plot_defects(defects_to_plot)

        # --- 3. Finalize 2D plot ---
        if self.current_plot_mode == '2D':
            # Update axis limits
            self.on_lineEdit_lim_textChanged()
            plotter.canvas.draw_idle()
            QCoreApplication.processEvents()  # 强制刷新界面

    def _update_defect_parameter_table(self):
        table = self.ui.tableWidget_defectParams
        table.clearContents() 

        analyzer_name = self._get_selected_defect_name()
        if not analyzer_name:
            table.setRowCount(0)
            return

        # Ask the orchestrator directly for the parameters by name.
        # This decouples the UI from the analyzer's implementation details.
        params = self.orchestrator.get_analyzer_params(analyzer_name)
        if not params:
            table.setRowCount(0)
            return
        table.setRowCount(len(params))

        for row, param_def in enumerate(params):
            # --- Column 0: Parameter Name ---
            name_item = QTableWidgetItem(param_def['name'])
            table.setItem(row, 0, name_item)
            id_item = QTableWidgetItem(param_def['id'])
            table.setItem(row, 2, id_item)
            table.setColumnHidden(2, True)
            type_item = QTableWidgetItem(param_def['type'])
            table.setItem(row, 3, type_item)
            table.setColumnHidden(3, True)

            # --- Column 1: Value Input Widget ---
            editor = None
            if param_def['type'] == 'float':
                editor = QLineEdit(str(param_def['default']))
                editor.setValidator(QDoubleValidator()) 
            elif param_def['type'] == 'int':
                editor = QLineEdit(str(param_def['default']))
                editor.setValidator(QIntValidator()) 
            elif param_def['type'] == 'bool':
                editor = QCheckBox()
                editor.setChecked(param_def['default'])

            if editor:
                table.setCellWidget(row, 1, editor)

#Event
    def closeEvent(self, event):
        """
        Custom close event handler to ensure graceful shutdown of resources, especially the PyVista OpenGL context.
        """

        try:
            # Explicitly close the 3D plotter to release its OpenGL context before the main window is destroyed. This prevents VTK errors.
            if self.plt_3D:
                self.plt_3D.close()
        except Exception as e:
            print(f"Error during 3D plotter cleanup: {e}")
        
        # It's good practice to explicitly accept the event to ensure the window proceeds with closing.
        event.accept()
