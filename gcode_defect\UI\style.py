def apply_style(self):
    style = """
    /* 主窗口 - 简洁白色背景 */
    QMainWindow {
        background-color: #ffffff;
        color: #333333;
    }

    QWidget {
        background-color: #ffffff;
        color: #333333;
        font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
        font-size: 10pt;
    }

    /* 标签 - 简洁文字 */
    QLabel {
        color: #555555;
        font-weight: normal;
        font-size: 10pt;
        background: transparent;
    }

    /* 按钮 - 极简设计 */
    QPushButton {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 6px 12px;
        min-height: 20px;
        font-weight: normal;
        color: #495057;
        font-size: 10pt;
    }
    QPushButton:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }
    QPushButton:pressed {
        background-color: #dee2e6;
        border-color: #6c757d;
    }
    QPushButton:disabled {
        background-color: #f8f9fa;
        border-color: #dee2e6;
        color: #adb5bd;
    }

    /* 复选框 - 简洁样式 */
    QCheckBox {
        font-size: 10pt;
        color: #495057;
        spacing: 6px;
    }
    QCheckBox::indicator {
        width: 14px;
        height: 14px;
        border: 1px solid #ced4da;
        border-radius: 2px;
        background-color: #ffffff;
    }
    QCheckBox::indicator:checked {
        background-color: #007bff;
        border-color: #007bff;
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggM0w0IDdMMiA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
    }
    QCheckBox::indicator:hover {
        border-color: #007bff;
    }

    /* 单选按钮 - 简洁样式 */
    QRadioButton {
        font-size: 10pt;
        color: #495057;
        spacing: 6px;
    }
    QRadioButton::indicator {
        width: 14px;
        height: 14px;
        border: 1px solid #ced4da;
        border-radius: 7px;
        background-color: #ffffff;
    }
    QRadioButton::indicator:checked {
        background-color: #007bff;
        border-color: #007bff;
    }
    QRadioButton::indicator:hover {
        border-color: #007bff;
    }

    /* 下拉框 - 简洁样式 */
    QComboBox {
        background-color: #ffffff;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 10pt;
        color: #495057;
        min-height: 20px;
    }
    QComboBox:hover {
        border-color: #007bff;
    }
    QComboBox:focus {
        border-color: #007bff;
        outline: none;
    }
    QComboBox::drop-down {
        border: none;
        width: 20px;
    }
    QComboBox::down-arrow {
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMgNUw2IDhMOSA1IiBzdHJva2U9IiM2Yzc1N2QiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
    }

    /* 输入框 - 简洁样式 */
    QLineEdit {
        background-color: #ffffff;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 10pt;
        color: #495057;
        min-height: 20px;
    }
    QLineEdit:hover {
        border-color: #007bff;
    }
    QLineEdit:focus {
        border-color: #007bff;
        outline: none;
    }
    /* 分组框 - 简洁边框 */
    QGroupBox {
        font-weight: 500;
        font-size: 10pt;
        color: #495057;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        margin-top: 8px;
        padding-top: 8px;
        background-color: #ffffff;
    }
    QGroupBox::title {
        subcontrol-origin: margin;
        subcontrol-position: top left;
        padding: 0 8px;
        color: #495057;
        background-color: #ffffff;
    }

    /* 标签页 - 简洁设计 */
    QTabWidget::pane {
        border: 1px solid #e9ecef;
        background-color: #ffffff;
        border-radius: 4px;
    }
    QTabWidget::tab-bar {
        alignment: left;
    }
    QTabBar::tab {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-bottom: none;
        padding: 8px 16px;
        margin-right: 2px;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        color: #6c757d;
        font-size: 10pt;
    }
    QTabBar::tab:selected {
        background-color: #ffffff;
        color: #495057;
        border-color: #e9ecef;
        border-bottom: 1px solid #ffffff;
    }
    QTabBar::tab:hover:!selected {
        background-color: #e9ecef;
        color: #495057;
    }

    /* 滑块 - 简洁样式 */
    QSlider::groove:horizontal {
        height: 4px;
        background: #e9ecef;
        border-radius: 2px;
    }
    QSlider::handle:horizontal {
        background: #007bff;
        border: none;
        width: 16px;
        height: 16px;
        margin: -6px 0;
        border-radius: 8px;
    }
    QSlider::handle:horizontal:hover {
        background: #0056b3;
    }
    QSlider::groove:vertical {
        width: 4px;
        background: #e9ecef;
        border-radius: 2px;
    }
    QSlider::handle:vertical {
        background: #007bff;
        border: none;
        width: 16px;
        height: 16px;
        margin: 0 -6px;
        border-radius: 8px;
    }
    QSlider::handle:vertical:hover {
        background: #0056b3;
    }

    /* 滚动条 - 极简样式 */
    QScrollBar:vertical {
        background: transparent;
        width: 8px;
        margin: 0;
        border: none;
    }
    QScrollBar::handle:vertical {
        background: #ced4da;
        min-height: 20px;
        border-radius: 4px;
        margin: 2px;
    }
    QScrollBar::handle:vertical:hover {
        background: #adb5bd;
    }
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
        height: 0px;
        border: none;
    }
    QScrollBar:horizontal {
        background: transparent;
        height: 8px;
        margin: 0;
        border: none;
    }
    QScrollBar::handle:horizontal {
        background: #ced4da;
        min-width: 20px;
        border-radius: 4px;
        margin: 2px;
    }
    QScrollBar::handle:horizontal:hover {
        background: #adb5bd;
    }
    QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
        width: 0px;
        border: none;
    }

    /* 菜单栏 - 简洁样式 */
    QMenuBar {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        color: #495057;
        font-size: 10pt;
    }
    QMenuBar::item {
        padding: 6px 12px;
        background: transparent;
    }
    QMenuBar::item:selected {
        background-color: #e9ecef;
    }

    /* 状态栏 - 简洁样式 */
    QStatusBar {
        background-color: #f8f9fa;
        border-top: 1px solid #e9ecef;
        color: #6c757d;
        font-size: 9pt;
    }

    /* matplotlib区域 - 无边框 */
    QWidget[objectName="matplotlib_area"] {
        border: 1px solid #e9ecef;
        border-radius: 4px;
        background-color: #ffffff;
    }

    /* 布局容器 - 透明背景防止遮挡文字 */
    QWidget[objectName="gridLayoutWidget"],
    QWidget[objectName="horizontalLayoutWidget"],
    QWidget[objectName="horizontalLayoutWidget_2"],
    QWidget[objectName="horizontalLayoutWidget_3"],
    QWidget[objectName="horizontalLayoutWidget_5"],
    QWidget[objectName="horizontalLayoutWidget_7"],
    QWidget[objectName="gridLayoutWidget_2"],
    QWidget[objectName="gridLayoutWidget_3"] {
        background-color: transparent;
    }

    /* 所有布局容器透明 */
    QGridLayout, QHBoxLayout, QVBoxLayout {
        background-color: transparent;
    }

    /* 确保所有布局widget透明 */
    QWidget[class="QWidget"] {
        background-color: transparent;
    }
    """
    self.setStyleSheet(style)