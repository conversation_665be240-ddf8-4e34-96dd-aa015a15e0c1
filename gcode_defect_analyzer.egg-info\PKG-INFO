Metadata-Version: 2.4
Name: gcode-defect-analyzer
Version: 0.1.0
Summary: A tool to analyze G-code files for 3D printing defects.
Author-email: DaiXiong <<EMAIL>>
License-Expression: MIT
Project-URL: Homepage, https://gitlab.bambu-lab.com/systest/g-code-defect
Classifier: Programming Language :: Python :: 3
Classifier: Operating System :: OS Independent
Classifier: Topic :: Scientific/Engineering
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: scipy
Requires-Dist: pyvistaqt
Requires-Dist: pyvista
Requires-Dist: scikit-learn
Requires-Dist: shapely
Requires-Dist: rtree<1.0
Requires-Dist: numpy
Requires-Dist: matplotlib
Requires-Dist: PySide6

Install dependencies: 'pip install -e.'
