import numpy as np
from shapely.geometry import <PERSON>ygon, LineString, MultiPolygon, CAP_STYLE, JOIN_STYLE

def get_line_width_polygon_points(stPos: tuple[float, float], endPos: tuple[float, float], width: float):
    """Calculate the four corner points of a rectangle given its center line and width."""
    if width > 0:
        dx = endPos[0] - stPos[0]
        dy = endPos[1] - stPos[1]
        length = np.hypot(dx, dy)         
            
        ux, uy = dx / length, dy / length
        nx, ny = -uy, ux  # Normal vector
        half_w = width / 2.0
        
        p1 = (stPos[0] + nx * half_w, stPos[1] + ny * half_w)
        p2 = (stPos[0] - nx * half_w, stPos[1] - ny * half_w)
        p3 = (endPos[0] - nx * half_w, endPos[1] - ny * half_w)
        p4 = (endPos[0] + nx * half_w, endPos[1] + ny * half_w)
        
        return[p1, p2, p3, p4]
    else:
        return None


def build_line_width_polygon(start: tuple, end: tuple, width: float) -> Polygon:
    """Build a polygon representing a line with width."""
    pts = get_line_width_polygon_points(start, end, width)
    if pts:
        poly = Polygon(pts)
    return poly if poly and poly.is_valid else None


def get_arc_width_polygon_points(start: tuple, end: tuple, center: tuple, clockwise: bool, width: float, **kwargs) -> Polygon:
    """Calculate the points of an arc with width."""
    from gcode_defect.utils.line_segment import get_arc_sample_points

    radius = np.linalg.norm(np.array(start) - np.array(center))
    if width > 0:
        r_outer = radius + width / 2.0
        r_inner = max(0, radius - width / 2.0)
    else:
        return None
    chord_error_mm = kwargs.get('chord_error_mm', 0.01)
    max_discrete_num = kwargs.get('max_discrete_num', 100)

    samp_angles, _ = get_arc_sample_points(start, end, center, clockwise, chord_error_mm, max_discrete_num)
        
    cx, cy = center[:2]
    outer_pts = [(cx + r_outer * np.cos(a), cy + r_outer * np.sin(a)) for a in samp_angles]
    inner_pts = [(cx + r_inner * np.cos(a), cy + r_inner * np.sin(a)) for a in samp_angles[::-1]] # Inner ring in reverse for closure

    return outer_pts, inner_pts


def build_arc_width_polygon(start: tuple, end: tuple, center: tuple, clockwise: bool, width: float, **kwargs) -> Polygon:
    """Build a polygon representing an arc with width."""
    outer_pts, inner_pts = get_arc_width_polygon_points(start, end, center, clockwise, width, **kwargs)
    ring = outer_pts + inner_pts
    if len(ring) < 3:
        return None
    else: 
        poly = Polygon(ring)
        return poly if poly.is_valid else None
    

from gcode_defect.GcodeParse.ModelInfo import TrackInfo
def build_closed_tracks_polygon(contour: list[TrackInfo], is_G1_discrete = False, G1_samp_length = 1) -> Polygon | None:
    """Build a closed polygon from a contour of tracks."""
    from gcode_defect.utils.line_segment import translate_continious_tracks_into_pts

    if not contour:
        return None
    
    is_closed = np.linalg.norm(np.array(contour[0].stPos[:2]) - np.array(contour[-1].endPos[:2])) < 0.2
    if not is_closed:
        return None
    
    points = translate_continious_tracks_into_pts(contour, is_G1_discrete, G1_samp_length)
    points_2D = [p[:2] for p in points]
    if len(points_2D) < 3:
        return None
    
    # Check if polygon is valid
    polygon = Polygon(points_2D)
    if polygon.is_valid and not polygon.is_empty:
        return polygon
    else:
        # Try to repair the polygon using the buffer(0) trick
        repaired_polygon = polygon.buffer(0)
        if repaired_polygon.is_valid and not repaired_polygon.is_empty:
            return repaired_polygon  
        
        # from gcode_defect.utils.debug_functions import debug_points
        # debug_points(points) # If still invalid, then export for debugging
        return None

def continious_line_width_model(points: list[tuple[float, float]], widths: list[float], closure_tolerance: float = 0.1) -> list[Polygon] | None:
    """
    Creates a polygon from a polyline with a variable width.
    This function uses a fast NumPy-based method and falls back to shapely's robust
    buffer only when the fast method produces an invalid (self-intersecting) polygon.

    Args:
        points: A list of (x, y) tuples for the line's vertices.
        widths: A list of width values, one for each point in the line.
        closure_tolerance: Distance to consider the polyline closed.

    Returns:
        A list of valid shapely Polygon objects, or None if input is invalid.
    """
    if len(points) < 2 or len(points) != len(widths):
        return None

    line_points = np.array(points, dtype=float)
    half_widths = np.array(widths, dtype=float) / 2.0

    # --- 1. Check if the polyline is a closed loop ---
    is_closed = False
    # A line needs at least 3 points to be considered for closure.
    if len(points) > 2 and np.linalg.norm(line_points[0] - line_points[-1]) < closure_tolerance:
        is_closed = True
        # For calculations, explicitly close the loop
        line_points = np.vstack([line_points, line_points[0]])
        half_widths = np.append(half_widths, half_widths[0])

    # --- 2. Fast polygon generation using NumPy ---
    polygon_vertices = _fast_mitered_offset(line_points, half_widths, is_closed)

    if polygon_vertices is None or len(polygon_vertices) < 3:
        # Fallback to robust buffer if fast method fails to produce vertices
        return _robust_buffer_fallback(line_points, half_widths, is_closed)

    # --- 3. Validate and Fix ---
    try:
        fast_polygon = Polygon(polygon_vertices)
        if fast_polygon.is_valid:
            # Fast method succeeded and polygon is valid
            buffered_result = fast_polygon
        else:
            # Fast method produced a self-intersecting polygon, fix it with buffer(0)
            buffered_result = fast_polygon.buffer(0)
    except Exception:
        # If Polygon creation fails for any reason, fallback to robust buffer
        return _robust_buffer_fallback(line_points, half_widths, is_closed)

    # --- 4. Unify result to a list of Polygons ---
    if not buffered_result or buffered_result.is_empty:
        return None

    polygons = []
    if isinstance(buffered_result, Polygon):
        polygons.append(buffered_result)
    elif isinstance(buffered_result, MultiPolygon):
        polygons.extend([p for p in buffered_result.geoms if not p.is_empty])

    return polygons if polygons else None

# --- Private functions ---
def _fast_mitered_offset(line_points: np.ndarray, half_widths: np.ndarray, is_closed: bool) -> np.ndarray | None:
    """
    Calculates the vertices of a polygon by offsetting a polyline with mitered joins.
    This is a fast, NumPy-based alternative to shapely's buffer, but it does not handle self-intersections.
    """
    if len(line_points) < 2:
        return None

    # 1. Calculate tangent and normal vectors for each segment
    segments = np.diff(line_points, axis=0)
    lengths = np.linalg.norm(segments, axis=1)

    # Filter out zero-length segments to avoid division by zero
    valid_segments = lengths > 1e-9
    if not np.any(valid_segments): return None

    # Normalize tangents
    tangents = np.zeros_like(segments)
    tangents[valid_segments] = segments[valid_segments] / lengths[valid_segments, np.newaxis]

    # Normals are tangents rotated by 90 degrees
    normals = np.column_stack([-tangents[:, 1], tangents[:, 0]])

    # 2. Calculate offset points
    left_pts = []
    right_pts = []

    # --- Start Cap (for open lines) ---
    if not is_closed:
        offset_vec = normals[0] * half_widths[0]
        left_pts.append(line_points[0] + offset_vec)
        right_pts.append(line_points[0] - offset_vec)

    # --- Mitered Joins (for interior vertices) ---
    num_joins = len(line_points) - 1 if is_closed else len(line_points) - 2

    for i in range(num_joins):
        p = line_points[i + 1]
        w = half_widths[i + 1]

        n_in = normals[i]
        n_out = normals[(i + 1) % len(normals)]

        miter_vec = n_in + n_out
        miter_norm = np.linalg.norm(miter_vec)

        if miter_norm < 1e-9:  # Segments are collinear and opposite
            miter_vec = n_in  # Fallback to the incoming normal
            miter_len_factor = 1.0
        else:
            miter_vec /= miter_norm
            # Calculate the miter length adjustment factor to maintain constant width
            cos_val = np.dot(miter_vec, n_in)
            if cos_val < 1e-9:  # Angle is too sharp, cap the miter to prevent extreme spikes
                miter_len_factor = 5.0
            else:
                miter_len_factor = 1.0 / cos_val

        offset_dist = w * miter_len_factor
        left_pts.append(p + miter_vec * offset_dist)
        right_pts.append(p - miter_vec * offset_dist)

    # --- End Cap (for open lines) ---
    if not is_closed:
        offset_vec = normals[-1] * half_widths[-1]
        left_pts.append(line_points[-1] + offset_vec)
        right_pts.append(line_points[-1] - offset_vec)
    else:
        # For a closed loop, close the polygon by connecting back to the start
        left_pts.append(left_pts[0])
        right_pts.append(right_pts[0])

    # 3. Assemble the final polygon vertices
    if not left_pts or not right_pts:
        return None

    # The right points need to be in reverse order to form a continuous loop
    return np.array(left_pts + right_pts[::-1])

def _robust_buffer_fallback(line_points: np.ndarray, half_widths: np.ndarray, is_closed: bool) -> list[Polygon] | None:
    """The original robust but slow shapely buffer method."""
    line_string = LineString(line_points)
    buffered_result = line_string.buffer(
        distance=half_widths,
        join_style=JOIN_STYLE.round,
        cap_style=CAP_STYLE.round if not is_closed else CAP_STYLE.flat
    )

    if not buffered_result or buffered_result.is_empty:
        return None

    polygons = []
    if isinstance(buffered_result, Polygon):
        polygons.append(buffered_result)
    elif isinstance(buffered_result, MultiPolygon):
        # Decompose MultiPolygon into a list of Polygons
        polygons.extend([p for p in buffered_result.geoms if not p.is_empty])
    elif isinstance(buffered_result, np.ndarray):
        # Filter the numpy array for valid Polygons
        polygons.extend([p for p in buffered_result if isinstance(p, Polygon) and not p.is_empty])

    return polygons if polygons else None